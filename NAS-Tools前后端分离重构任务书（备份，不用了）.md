# NAS-Tools 前后端分离重构任务书

## 项目概述

### 项目背景
NAS-Tools是一个功能完善的NAS媒体库管理工具，主要用于PT站点管理、媒体下载、刷流等功能。当前采用传统的Flask单体应用架构，前后端耦合，使用Jinja2模板渲染。为了提升用户体验、开发效率和系统可维护性，需要进行前后端分离重构。

### 重构目标
- 🎯 实现前后端完全分离，提升开发效率和用户体验
- 🚀 采用现代化前端技术栈，提供更好的交互体验
- 🔧 优化API设计，提供更标准化的接口服务
- 📱 支持响应式设计，适配多种设备
- 🔌 为未来的移动端应用和第三方集成提供基础

---

## 第一阶段：现状分析

### 1.1 前端架构分析

#### 当前技术栈
- **模板引擎**: Jinja2
- **UI框架**: Tabler UI (Bootstrap-based)
- **JavaScript库**: jQuery 3.3.1
- **构建工具**: 无现代化构建工具
- **组件化**: 基础的HTML组件，缺乏现代化组件系统

#### 目录结构分析
```
web/
├── static/
│   ├── components/     # 基础组件（有限）
│   ├── css/           # 样式文件
│   ├── js/            # JavaScript文件
│   └── img/           # 图片资源
├── templates/         # Jinja2模板
│   ├── index.html     # 主页模板
│   ├── login.html     # 登录页面
│   ├── setting/       # 设置页面模板
│   ├── download/      # 下载管理模板
│   └── ...           # 其他功能模板
└── main.py           # Flask主应用
```

#### 路由和页面结构
- **主要页面**: 首页、搜索、下载、订阅、RSS、设置、站点管理等
- **导航结构**: 侧边栏导航，支持多级菜单
- **页面交互**: 基于jQuery的AJAX请求和DOM操作

#### 状态管理
- **当前方案**: 无统一状态管理，依赖DOM状态和全局变量
- **数据流**: 通过AJAX请求获取数据，直接操作DOM更新界面

#### 构建和部署
- **构建方式**: 静态文件直接部署，无构建优化
- **部署方式**: 与后端一起打包部署

### 1.2 后端通信分析

#### API接口梳理
基于Flask-RESTX的RESTful API，包含以下主要模块：

| 模块 | 命名空间 | 主要功能 | 接口数量 |
|------|----------|----------|----------|
| 用户管理 | `/user` | 登录、认证、用户信息 | 3个 |
| 系统管理 | `/system` | 系统信息、重启、路径查询 | 5个 |
| 配置管理 | `/config` | 配置读取、备份、恢复 | 8个 |
| 站点管理 | `/site` | 站点配置、统计、测试 | 12个 |
| 服务管理 | `/service` | 媒体信息识别等服务 | 3个 |
| 订阅管理 | `/subscribe` | 电影/电视剧订阅 | 8个 |
| RSS管理 | `/rss` | 自定义RSS订阅 | 10个 |
| 推荐系统 | `/recommend` | 媒体推荐 | 2个 |
| 搜索下载 | `/search` `/download` | 资源搜索和下载 | 15个 |
| 媒体整理 | `/organization` | 文件整理和重命名 | 6个 |
| 刷流任务 | `/brushtask` | 自动刷流管理 | 8个 |
| 媒体库 | `/library` `/media` | 媒体库管理 | 12个 |
| 目录同步 | `/sync` | 目录同步服务 | 6个 |
| 过滤规则 | `/filterrule` | 内容过滤规则 | 5个 |
| 识别词 | `/words` | 自定义识别词 | 6个 |
| 消息通知 | `/message` | 消息推送配置 | 4个 |
| 插件系统 | `/plugin` | 插件管理 | 8个 |

#### 认证机制
- **认证方式**: Bearer Token + API Key双重认证
- **权限控制**: 基于装饰器的权限验证
- **会话管理**: Flask-Login会话管理

#### 数据格式
```json
{
  "code": 0,
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

#### 实时通信
- **WebSocket**: 使用Flask-Sock实现消息推送
- **EventSource**: 用于进度更新和日志推送

---

## 第二阶段：重构设计

### 2.1 技术栈选择

#### 前端技术栈
```json
{
  "核心框架": "Vue 3.4+",
  "开发语言": "TypeScript 5.0+",
  "构建工具": "Vite 5.0+",
  "UI组件库": "Element Plus 2.4+",
  "状态管理": "Pinia 2.1+",
  "路由管理": "Vue Router 4.2+",
  "HTTP客户端": "Axios 1.6+",
  "CSS预处理": "SCSS",
  "图标库": "Element Plus Icons + Tabler Icons",
  "工具库": "Lodash-es, Day.js"
}
```

#### 开发工具链
```json
{
  "代码规范": "ESLint + Prettier",
  "类型检查": "TypeScript",
  "测试框架": "Vitest + Vue Test Utils",
  "包管理器": "pnpm",
  "Git钩子": "Husky + lint-staged"
}
```

### 2.2 架构设计

#### 整体架构图
```mermaid
graph TB
    subgraph "前端应用"
        A[Vue 3 + TypeScript]
        B[Pinia 状态管理]
        C[Vue Router 路由]
        D[Element Plus UI]
        E[Axios HTTP客户端]
    end
    
    subgraph "API网关"
        F[Flask-RESTX API]
        G[WebSocket服务]
        H[认证中间件]
    end
    
    subgraph "后端服务"
        I[业务逻辑层]
        J[数据访问层]
        K[外部服务集成]
    end
    
    A --> E
    E --> F
    F --> I
    I --> J
    A --> G
    G --> I
```

#### 前端模块架构
```
frontend/
├── src/
│   ├── api/              # API接口定义
│   ├── components/       # 通用组件
│   ├── composables/      # 组合式函数
│   ├── layouts/          # 布局组件
│   ├── pages/            # 页面组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia状态管理
│   ├── styles/           # 全局样式
│   ├── types/            # TypeScript类型定义
│   ├── utils/            # 工具函数
│   └── main.ts           # 应用入口
├── public/               # 静态资源
├── tests/                # 测试文件
└── package.json          # 项目配置
```

#### 组件层次结构
```mermaid
graph TD
    A[App.vue] --> B[Layout布局]
    B --> C[Header头部]
    B --> D[Sidebar侧边栏]
    B --> E[Main主内容区]
    E --> F[Dashboard仪表板]
    E --> G[Search搜索页面]
    E --> H[Download下载管理]
    E --> I[Subscribe订阅管理]
    E --> J[Settings设置页面]
    
    subgraph "通用组件"
        K[DataTable数据表格]
        L[FormDialog表单弹窗]
        M[StatusCard状态卡片]
        N[ProgressBar进度条]
    end
```

### 2.3 API设计规范

#### RESTful API标准
```typescript
// 统一响应格式
interface ApiResponse<T = any> {
  code: number;
  success: boolean;
  message: string;
  data: T;
  timestamp?: number;
}

// 分页响应格式
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
```

#### API版本管理
- **版本策略**: URL路径版本控制 `/api/v1/`, `/api/v2/`
- **兼容性**: 保持向后兼容，逐步废弃旧版本
- **文档**: 使用Swagger自动生成API文档

#### 实时通信设计
```typescript
// WebSocket消息格式
interface WebSocketMessage {
  type: 'progress' | 'notification' | 'log' | 'status';
  data: any;
  timestamp: number;
}

// EventSource事件类型
type SSEEventType = 'progress' | 'log' | 'heartbeat';
```

#### API路由设计
```
/api/v2/
├── auth/                 # 认证相关
│   ├── login             # 登录
│   ├── logout            # 登出
│   └── profile           # 用户信息
├── dashboard/            # 仪表板数据
│   ├── stats             # 统计信息
│   └── activities        # 活动记录
├── media/                # 媒体管理
│   ├── movies            # 电影
│   ├── tv                # 电视剧
│   └── search            # 媒体搜索
├── download/             # 下载管理
│   ├── torrents          # 种子列表
│   ├── clients           # 下载客户端
│   └── tasks             # 下载任务
├── subscribe/            # 订阅管理
│   ├── movies            # 电影订阅
│   ├── tv                # 电视剧订阅
│   └── refresh           # 订阅刷新
├── sites/                # 站点管理
│   ├── list              # 站点列表
│   ├── stats             # 站点统计
│   └── test              # 站点测试
├── config/               # 配置管理
│   ├── system            # 系统配置
│   ├── user              # 用户配置
│   └── backup            # 配置备份
└── system/               # 系统管理
    ├── status            # 系统状态
    ├── logs              # 系统日志
    └── restart           # 系统重启
```

---

## 第三阶段：迁移计划

### 3.1 功能模块划分

#### 核心模块优先级

| 优先级 | 模块名称 | 功能描述 | 预估工时 | 依赖关系 |
|--------|----------|----------|----------|----------|
| P0 | 用户认证 | 登录、权限验证 | 3天 | 无 |
| P0 | 系统配置 | 基础配置管理 | 2天 | 用户认证 |
| P1 | 仪表板 | 系统状态概览 | 4天 | 用户认证 |
| P1 | 站点管理 | PT站点配置 | 5天 | 系统配置 |
| P1 | 搜索功能 | 资源搜索界面 | 6天 | 站点管理 |
| P2 | 下载管理 | 下载任务管理 | 7天 | 搜索功能 |
| P2 | 订阅管理 | 自动订阅配置 | 6天 | 下载管理 |
| P2 | RSS管理 | 自定义RSS | 5天 | 订阅管理 |
| P3 | 媒体库 | 媒体库管理 | 8天 | 下载管理 |
| P3 | 刷流任务 | 自动刷流 | 6天 | 站点管理 |
| P3 | 插件系统 | 插件管理界面 | 4天 | 系统配置 |

### 3.2 API迁移映射

#### 新旧API对照表

| 功能模块 | 旧API路径 | 新API路径 | 迁移状态 | 备注 |
|----------|-----------|-----------|----------|------|
| 用户登录 | `/api/v1/user/login` | `/api/v2/auth/login` | 🔄 需重构 | 优化响应格式 |
| 用户信息 | `/api/v1/user/info` | `/api/v2/auth/profile` | ✅ 兼容 | 保持兼容 |
| 站点列表 | `/api/v1/site/sites` | `/api/v2/sites` | 🔄 需重构 | 简化路径 |
| 搜索资源 | `/api/v1/search` | `/api/v2/search/resources` | 🔄 需重构 | 增加过滤参数 |
| 下载任务 | `/api/v1/download/list` | `/api/v2/downloads` | ✅ 兼容 | 保持兼容 |
| 订阅管理 | `/api/v1/subscribe/list` | `/api/v2/subscriptions` | 🔄 需重构 | 标准化命名 |

#### 需要新建的API接口

| 接口名称 | 路径 | 方法 | 功能描述 |
|----------|------|------|----------|
| 批量操作 | `/api/v2/batch` | POST | 支持批量删除、启用等操作 |
| 实时状态 | `/api/v2/status/realtime` | GET | 获取系统实时状态 |
| 配置验证 | `/api/v2/config/validate` | POST | 配置项验证 |
| 文件预览 | `/api/v2/files/preview` | GET | 文件内容预览 |

#### 需要废弃的接口

| 接口路径 | 废弃原因 | 替代方案 | 废弃时间 |
|----------|----------|----------|----------|
| `/api/v1/old_search` | 性能问题 | `/api/v2/search/resources` | v2.1版本 |
| `/api/v1/legacy_config` | 格式过时 | `/api/v2/config` | v2.2版本 |

### 3.3 实施时间线

#### 第一阶段：基础设施搭建 (2周)

**Week 1-2: 项目初始化**
- [x] 创建Vue 3 + TypeScript项目脚手架
- [x] 配置开发环境和构建工具
- [x] 设计UI组件库和设计系统
- [x] 搭建API接口层和类型定义
- [x] 配置状态管理和路由系统

**交付物:**
- ✅ 完整的前端项目结构
- ✅ 开发环境配置文档
- ✅ UI组件库和设计系统
- ✅ API接口类型定义

#### 第二阶段：核心功能迁移 (4周)

**Week 3-4: 用户认证和系统配置**
- [x] 实现登录页面和认证流程
- [x] 迁移用户管理相关API
- [x] 实现系统配置页面
- [x] 集成权限控制系统

**Week 5-6: 仪表板和站点管理**
- [x] 开发系统仪表板页面
- [x] 实现站点管理功能
- [x] 迁移站点相关API接口
- [x] 实现实时状态更新

**交付物:**
- ✅ 用户认证系统
- ✅ 系统仪表板
- ✅ 站点管理功能
- ✅ 系统配置页面
- ✅ 基础权限控制
- ✅ 实时状态更新

#### 第三阶段：业务功能迁移 (6周)

**Week 7-8: 搜索和下载功能**
- [ ] 实现资源搜索页面
- [ ] 迁移搜索相关API
- [ ] 开发下载管理界面
- [ ] 实现下载进度实时更新

**Week 9-10: 订阅和RSS管理**
- [ ] 实现订阅管理功能
- [ ] 迁移订阅相关API
- [ ] 开发RSS管理界面
- [ ] 实现自动订阅配置

**Week 11-12: 媒体库和高级功能**
- [ ] 实现媒体库管理
- [ ] 开发刷流任务管理
- [ ] 迁移插件系统界面
- [ ] 实现批量操作功能

**交付物:**
- ✅ 完整的搜索下载功能
- ✅ 订阅和RSS管理
- ✅ 媒体库管理界面
- ✅ 高级功能模块

#### 第四阶段：优化和测试 (2周)

**Week 13-14: 性能优化和测试**
- [ ] 前端性能优化
- [ ] 编写单元测试和集成测试
- [ ] 用户体验优化
- [ ] 兼容性测试和修复

**交付物:**
- ✅ 性能优化报告
- ✅ 完整的测试覆盖
- ✅ 用户使用文档
- ✅ 部署指南

---

## 第四阶段：详细实施指南

### 4.1 前端开发规范

#### 代码组织结构
```
src/
├── api/                  # API接口封装
│   ├── modules/          # 按模块分类的API
│   │   ├── auth.ts       # 认证相关API
│   │   ├── media.ts      # 媒体相关API
│   │   └── system.ts     # 系统相关API
│   └── index.ts          # API统一导出
├── assets/               # 静态资源文件
│   ├── icons/            # 图标文件
│   └── images/           # 图片资源
├── components/           # 公共组件
│   ├── base/             # 基础组件
│   ├── business/         # 业务组件
│   └── layout/           # 布局组件
├── composables/          # 组合式函数
│   ├── useAuth.ts        # 认证相关组合函数
│   ├── useApi.ts         # API调用组合函数
│   └── useWebSocket.ts   # WebSocket相关组合函数
├── layouts/              # 页面布局
│   ├── DefaultLayout.vue # 默认布局
│   └── AuthLayout.vue    # 认证页面布局
├── pages/                # 页面组件
│   ├── auth/             # 认证相关页面
│   ├── dashboard/        # 仪表板页面
│   └── media/            # 媒体相关页面
├── router/               # 路由配置
│   ├── routes/           # 路由定义
│   └── index.ts          # 路由实例
├── stores/               # 状态管理
│   ├── modules/          # 按模块分类的状态
│   │   ├── auth.ts       # 认证状态
│   │   ├── media.ts      # 媒体状态
│   │   └── system.ts     # 系统状态
│   └── index.ts          # 状态管理实例
├── styles/               # 样式文件
│   ├── variables.scss    # 全局变量
│   ├── mixins.scss       # 混合样式
│   └── index.scss        # 样式入口
├── types/                # TypeScript类型定义
│   ├── api.ts            # API相关类型
│   ├── store.ts          # 状态相关类型
│   └── global.ts         # 全局类型
├── utils/                # 工具函数
│   ├── date.ts           # 日期工具
│   ├── format.ts         # 格式化工具
│   └── request.ts        # 请求工具
└── main.ts               # 应用入口文件
```

#### 组件开发规范
1. **命名规范**:
   - 组件名使用 PascalCase 命名法
   - 组件文件名与组件名一致
   - 组件属性使用 camelCase 命名法

2. **组件结构**:
   ```vue
   <template>
     <!-- 组件模板 -->
   </template>
   
   <script setup lang="ts">
   // 组件逻辑
   </script>
   
   <style scoped>
   /* 组件样式 */
   </style>
   ```

3. **Props 定义**:
   ```typescript
   interface Props {
     title: string
     visible?: boolean
     onClose?: () => void
   }
   
   withDefaults(defineProps<Props>(), {
     visible: false
   })
   ```

#### 状态管理规范
1. **模块化管理**: 按功能模块划分状态
2. **命名规范**: 使用动词+名词的形式命名 action
3. **数据流**: 单向数据流，通过 actions 修改状态

#### API 调用规范
1. **统一封装**: 所有 API 调用通过统一的请求封装
2. **错误处理**: 统一的错误处理机制
3. **类型安全**: 为每个 API 接口定义 TypeScript 类型

### 4.2 后端API开发规范

#### API 版本控制
1. **URL 路径版本控制**: `/api/v1/`, `/api/v2/`
2. **向后兼容**: 保持旧版本 API 的兼容性
3. **废弃策略**: 明确标记废弃接口并提供替代方案

#### 响应格式规范
```json
{
  "code": 0,
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

#### 错误处理规范
```json
{
  "code": 400,
  "success": false,
  "message": "请求参数错误",
  "data": {
    "field": "name",
    "reason": "不能为空"
  },
  "timestamp": 1640995200000
}
```

#### 权限控制规范
1. **角色权限**: 基于角色的访问控制 (RBAC)
2. **接口权限**: 每个接口明确权限要求
3. **数据权限**: 根据用户权限返回相应数据

### 4.3 前后端协作规范

#### 接口文档规范
1. **Swagger 文档**: 使用 Swagger 自动生成 API 文档
2. **接口示例**: 提供详细的请求/响应示例
3. **错误码说明**: 明确各错误码的含义和处理方式

#### 联调测试规范
1. **接口测试**: 使用 Postman 或 Swagger 进行接口测试
2. **Mock 数据**: 开发阶段使用 Mock 数据
3. **联调环境**: 建立专门的联调测试环境

#### 部署规范
1. **前后端分离部署**: 前端静态文件独立部署
2. **API 网关**: 使用 Nginx 或 API 网关统一入口
3. **CORS 配置**: 正确配置跨域资源共享

---

## 风险评估和应对策略

### 技术风险

| 风险项 | 风险等级 | 影响 | 应对策略 |
|--------|----------|------|----------|
| API兼容性问题 | 🔴 高 | 功能异常 | 制定详细的API测试计划，保持向后兼容 |
| 数据迁移风险 | 🟡 中 | 数据丢失 | 建立完整的数据备份和回滚机制 |
| 性能下降 | 🟡 中 | 用户体验 | 进行性能基准测试，优化关键路径 |
| 浏览器兼容性 | 🟢 低 | 部分用户无法使用 | 明确支持的浏览器版本，提供降级方案 |

### 项目风险

| 风险项 | 风险等级 | 影响 | 应对策略 |
|--------|----------|------|----------|
| 开发进度延期 | 🟡 中 | 项目延期 | 采用敏捷开发，分阶段交付 |
| 人员技能不足 | 🟡 中 | 质量问题 | 提供技术培训，代码审查机制 |
| 需求变更频繁 | 🟡 中 | 范围蔓延 | 建立需求变更控制流程 |

### 应对措施

1. **技术预研**: 在正式开发前进行技术可行性验证
2. **分阶段交付**: 采用MVP方式，优先交付核心功能
3. **自动化测试**: 建立完整的自动化测试体系
4. **监控告警**: 实施完善的监控和告警机制
5. **回滚方案**: 制定详细的回滚和应急预案

---

## 验收标准

### 功能验收标准

- [ ] **用户认证**: 支持安全登录、权限控制、会话管理
- [ ] **界面响应**: 所有页面加载时间 < 2秒
- [ ] **实时更新**: WebSocket连接稳定，消息推送及时
- [ ] **数据一致性**: 前后端数据同步准确
- [ ] **错误处理**: 友好的错误提示和异常处理

### 性能验收标准

- [ ] **首屏加载**: < 3秒
- [ ] **页面切换**: < 1秒
- [ ] **API响应**: 平均响应时间 < 500ms
- [ ] **内存使用**: 前端内存占用 < 100MB
- [ ] **并发支持**: 支持100+并发用户

### 兼容性验收标准

- [ ] **浏览器支持**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- [ ] **设备适配**: 桌面端、平板、手机响应式适配
- [ ] **API兼容**: 保持与现有客户端的API兼容性

---

## 总结

本重构任务书提供了从传统Flask单体应用向现代化前后端分离架构迁移的完整方案。通过采用Vue 3 + TypeScript技术栈，结合现代化的开发工具和最佳实践，将显著提升NAS-Tools的用户体验和开发效率。

整个重构过程预计需要14周时间，分为4个阶段逐步实施。通过详细的风险评估和应对策略，确保重构过程的平稳进行。最终交付的系统将具备更好的可维护性、扩展性和用户体验。

### 关键成功因素

1. **团队技能**: 确保团队具备Vue 3、TypeScript等现代前端技术能力
2. **渐进式迁移**: 采用渐进式迁移策略，降低风险
3. **用户反馈**: 及时收集用户反馈，持续优化用户体验
4. **质量保证**: 建立完善的测试和质量保证体系
5. **文档完善**: 提供完整的开发和使用文档

### 后续规划

重构完成后，可以考虑以下后续优化：

- **移动端应用**: 基于统一API开发移动端应用
- **微服务架构**: 进一步拆分后端服务，实现微服务架构
- **性能优化**: 持续优化系统性能，提升用户体验
- **功能扩展**: 基于新架构快速迭代新功能
- **生态建设**: 开放API，支持第三方插件和集成