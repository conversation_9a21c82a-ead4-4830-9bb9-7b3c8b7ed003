# 里程碑2任务

## 项目背景
NAS-Tools前后端分离重构项目，基于Vue 3.5 + TypeScript 5.8 + Element Plus的现代化前端重构。**里程碑1已完成**基础设施和核心组件开发，现在需要进入**里程碑2的核心功能迁移阶段**。

## 当前项目状态
- ✅ **里程碑1已完成**: 基础开发环境、通用组件库、权限控制系统、布局主题系统全部就绪
- ✅ **开发环境**: 前端服务器运行在 http://localhost:3001，后端运行在 http://localhost:3000
- ✅ **技术栈**: Vue 3.5 + TypeScript 5.8 + Vite 7.0 + Element Plus + Pinia + Vue Router
- ✅ **代码质量**: 所有代码按生产级标准编写，无测试痕迹，构建和开发环境验证通过

## 里程碑2任务目标
**第二阶段：核心功能迁移 (4周)** - 实现用户认证、仪表板、系统管理、站点管理、搜索功能等核心页面功能

## 具体任务清单

### Week 3: 用户认证和系统管理 (优先级：P0)
- [ ] **完善JWT认证系统 (任务1)**
  - 参考文件: 旧版 `web/templates/login.html`, `web/apiv1.py` (user namespace)
  - 新版位置: `frontend/src/pages/auth/`, `web/apiv2.py` (auth_ns)
  - 参考项目: 参考前端目录下项目Geeker-Admin的登录页面和权限管理
  - 迁移重点: JWT Token认证机制、权限控制系统、登录状态持久化
  - 预估工时: 2天

- [ ] **实现系统配置管理 (任务2)**
  - 参考文件: 旧版 `web/templates/setting/basic.html`, `web/apiv1.py` (config namespace)
  - 新版位置: `frontend/src/pages/settings/`, `web/apiv2.py` (config_ns)
  - 参考项目: 参考前端目录下项目Geeker-Admin的设置页面组件
  - 迁移重点: 配置项分类管理、实时配置验证、配置备份恢复
  - 预估工时: 3天

- [ ] **迁移系统管理相关API**
- [ ] **实现权限控制中间件**

### Week 4: 仪表板和监控 (优先级：P1)
- [ ] **开发系统仪表板 (任务3)**
  - 参考文件: 旧版 `web/templates/index.html`
  - 新版位置: `frontend/src/pages/dashboard/`
  - 参考项目: 参考前端目录下项目Geeker-Admin的仪表板组件、MoviePilot-Frontend的dashboard页面
  - 迁移重点: 系统状态实时监控、数据可视化图表、快速操作入口
  - 预估工时: 4天

- [ ] **实现实时状态监控**
- [ ] **集成数据可视化图表**
- [ ] **实现WebSocket实时通信**

### Week 5-6: 站点和搜索功能 (优先级：P1)
- [ ] **实现站点管理系统 (任务4)**
  - 参考文件: 旧版 `web/templates/site/`, `web/apiv1.py` (site namespace)
  - 新版位置: `frontend/src/pages/site/`, `web/apiv2.py` (sites_ns)
  - 参考项目: MoviePilot-Frontend的站点管理页面
  - 迁移重点: 站点配置表单、站点状态监控、批量操作功能
  - 预估工时: 5天

- [ ] **开发搜索功能界面 (任务5)**
  - 参考文件: 旧版 `web/templates/search.html`, `web/apiv1.py` (search namespace)
  - 新版位置: `frontend/src/pages/search/` (待创建)
  - 参考项目: MoviePilot-Frontend的搜索界面
  - 迁移重点: 高级搜索表单、搜索结果展示、搜索历史记录
  - 预估工时: 6天

- [ ] **迁移站点和搜索相关API**
- [ ] **实现批量操作功能**

## 关键注意事项

### 🔴 必须严格遵守的要求
1. **任务书为准**: 一切进度以 `@/nas-tools/NAS-Tools前后端分离重构任务书-重写版.md` 为准
2. **实时更新进度**: 完成每项任务后必须更新任务书，不能随便勾选未完成的任务
3. **按顺序完成**: 必须按照任务书的顺序逐项完成，不能跳跃
4. **生产代码标准**: 所有代码按生产级标准编写，不留测试痕迹
5. **功能验证**: 每完成一项功能必须验证可用性，确保前端功能正常

### 🟡 开发环境相关
- **Docker环境**: 当前在Docker容器内开发，已配置Alpine系统和Playwright MCP
- **后端重启**: 旧版本Python后端持续运行，需要重启时杀死 `python3 run.py` 即可自动重启
- **前端验证**: 使用 `npm run verify` 验证生产代码，`npm run dev` 启动开发服务器
- **构建验证**: 使用 `npm run build` 验证生产构建
- **Playwright MCP测试**: 已配置完成，可用于生产代码自动化验证

### 🟢 技术架构要点
- **组件复用**: 优先使用已实现的通用组件 (ProTable, SearchForm, PageContainer等)
- **权限控制**: 所有页面和功能都要集成权限系统 (usePermission, v-permission指令)
- **响应式设计**: 确保所有页面在桌面端和移动端都能正常使用
- **主题适配**: 支持明暗主题和多种主题色
- **类型安全**: 充分利用TypeScript类型系统，确保类型安全

## 现有资源和工具

### 已实现的核心组件
```typescript
// 基础组件
import { ProTable, SearchForm, PermissionWrapper } from '@/components/base'

// 业务组件  
import { StatusIndicator, ProgressBar, MediaCard } from '@/components/business'

// 布局组件
import { AppLayout, PageContainer, BreadcrumbNav } from '@/components/layout'
```

### 权限控制工具
```typescript
// Composable
import { usePermission } from '@/composables/usePermission'

// 工具函数
import { hasPermission, hasRole, PERMISSIONS } from '@/utils/permission'

// Vue指令: v-permission, v-role, v-permission-disabled
```

### API迁移映射表 (重要参考)
根据任务书第2.3节，关键API迁移对照：

**用户认证**:
- 旧: `POST /api/v1/user/login` → 新: `POST /api/v2/auth/login` ✅已完成
- 旧: `POST /api/v1/user/info` → 新: `GET /api/v2/auth/profile` ✅已完成
- 旧: `POST /api/v1/user/logout` → 新: `POST /api/v2/auth/logout` 🔄待实现

**系统管理**:
- 旧: `POST /api/v1/system/status` → 新: `GET /api/v2/system/status` ✅已完成
- 旧: `POST /api/v1/system/info` → 新: `GET /api/v2/system/info` ✅已完成
- 旧: `POST /api/v1/system/restart` → 新: `POST /api/v2/system/restart` ✅已完成

**站点管理**:
- 旧: `GET /api/v1/site/sites` → 新: `GET /api/v2/sites` ✅已完成
- 旧: `POST /api/v1/site/test` → 新: `POST /api/v2/sites/{id}/test` ✅已完成

**配置管理**:
- 旧: `POST /api/v1/config/get` → 新: `GET /api/v2/config/system` ✅已完成
- 旧: `POST /api/v1/config/save` → 新: `PUT /api/v2/config/system` ✅已完成

## 参考资源
- **设计参考**: Geeker-Admin项目 (`@/参考前端/Geeker-Admin/`)
- **业务参考**: MoviePilot-Frontend项目
- **API文档**: 现有后端API接口
- **UI组件**: Element Plus官方文档
- **任务书**: `@/nas-tools/NAS-Tools前后端分离重构任务书-重写版.md`

## 验证和交付标准

### 🎯 核心验证要求
1. **功能完整**: 所有页面功能正常，用户交互流畅
2. **权限集成**: 权限控制正确实现，无权限用户无法访问受限功能
3. **响应式适配**: 桌面端和移动端显示正常
4. **数据集成**: 与后端API正确对接，数据显示准确
5. **构建成功**: `npm run build` 成功，`npm run verify` 通过
6. **任务书更新**: 完成进度在任务书中正确标记

### 🤖 Playwright MCP自动化验证 (必须执行)

**每完成一个功能模块后，必须执行以下验证流程**:

```bash
# 1. 构建生产版本
cd /nas-tools/frontend
npm run build

# 2. 启动生产预览服务器
npm run preview  # http://localhost:4173

# 3. 启动后端服务 (另一个终端)
cd /nas-tools
python3 run.py  # http://localhost:3000
```

**使用Playwright MCP进行验证**:
- 🌐 **导航测试**: 访问功能页面，验证正确加载
- 📝 **表单测试**: 填写表单，验证提交和响应
- 🔘 **交互测试**: 点击按钮，验证功能正常
- 📱 **响应式测试**: 调整窗口大小，验证移动端适配
- 📸 **截图记录**: 保存测试证据和页面状态

**验证通过标准**:
- ✅ 页面能正常加载，无404或500错误
- ✅ 表单提交正常，数据正确保存
- ✅ 按钮点击有正确响应
- ✅ 页面跳转和导航正确
- ✅ 无JavaScript控制台错误
- ✅ 移动端界面布局正常

## 开始工作提示

### 🚀 标准开发流程
```bash
# 1. 查看当前项目状态
cd /nas-tools/frontend
npm run verify

# 2. 启动开发环境
npm run dev

# 3. 查看任务书了解详细要求
cat /nas-tools/NAS-Tools前后端分离重构任务书-重写版.md

# 4. 开始第一个任务：完善JWT认证系统
```

### 🧪 功能完成后的验证流程
```bash
# 1. 构建生产版本
npm run build

# 2. 启动生产预览 (新终端)
npm run preview

# 3. 启动后端服务 (新终端)
cd /nas-tools && python3 run.py

# 4. 使用Playwright MCP验证功能
# - 导航到相关页面
# - 测试核心功能
# - 验证响应式布局
# - 截图保存测试证据

# 5. 确认验证通过后更新任务书进度
```

### 🎭 Playwright MCP测试示例

**登录功能验证示例**:
```
1. 使用MCP导航到: http://localhost:4173
2. 验证页面标题包含 "NAS-Tools"
3. 填写用户名: admin
4. 填写密码: password
5. 点击登录按钮
6. 验证跳转到仪表板页面
7. 截图保存登录成功状态
```

**站点管理功能验证示例**:
```
1. 点击侧边栏"站点管理"菜单
2. 验证页面显示站点列表
3. 点击"添加站点"按钮
4. 验证弹出添加站点对话框
5. 测试表单填写和提交
6. 截图保存功能状态
```

**响应式测试示例**:
```
1. 调整浏览器窗口到移动端尺寸 (375x667)
2. 验证侧边栏变为移动端菜单
3. 测试移动端菜单展开/收起
4. 验证表格在移动端的响应式显示
5. 截图保存移动端界面
```

## 重要提醒
- 📋 **任务书是唯一权威**: 所有任务安排和进度更新都以任务书为准
- 🔄 **实时更新进度**: 每完成一项任务立即更新任务书状态
- ✅ **验证后再标记**: 确保功能真正可用后再标记为完成
- 🚫 **禁止跳跃**: 严格按顺序完成，不能跳过任何任务
- 💻 **生产代码**: 所有代码都要达到生产级标准

**目标**: 完成里程碑2后，系统应具备核心管理功能，可基本使用。

**祝工作顺利！如有疑问请查看任务书或现有代码实现。** 🚀
