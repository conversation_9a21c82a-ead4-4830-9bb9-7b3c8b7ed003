# NAS-Tools 前后端分离重构任务书 (重写版)

## 项目概述

### 项目背景
NAS-Tools是一个功能完善的NAS媒体库管理工具，主要用于PT站点管理、媒体下载、刷流等功能。当前采用传统的Flask单体应用架构，前后端耦合，使用Jinja2模板渲染。为了提升用户体验、开发效率和系统可维护性，需要进行前后端分离重构。

### 重构目标
- 🎯 实现前后端完全分离，提升开发效率和用户体验
- 🚀 采用现代化前端技术栈，提供更好的交互体验
- 🔧 优化API设计，提供更标准化的接口服务
- 📱 支持响应式设计，适配多种设备
- 🔌 为未来的移动端应用和第三方集成提供基础

---

## 第一阶段：现状分析

### 1.1 旧版前端架构完整分析

#### 当前技术栈
- **模板引擎**: Jinja2
- **UI框架**: Tabler UI (Bootstrap-based)
- **JavaScript库**: jQuery 3.3.1
- **构建工具**: 无现代化构建工具
- **组件化**: 基础的HTML组件，缺乏现代化组件系统

#### 完整功能模块清单 (基于web/templates/)

**核心功能模块**:
1. **首页** (`index.html`) - 系统概览和快速操作
2. **登录** (`login.html`) - 用户认证
3. **搜索** (`search.html`) - 资源搜索
4. **服务** (`service.html`) - 服务管理

**发现推荐模块** (`discovery/`):
- `mediainfo.html` - 媒体信息展示
- `person.html` - 人物信息
- `ranking.html` - 排行榜
- `recommend.html` - 推荐内容

**下载管理模块** (`download/`):
- `downloading.html` - 下载中任务
- `torrent_remove.html` - 种子删除管理

**RSS管理模块** (`rss/`):
- `movie_rss.html` - 电影RSS订阅
- `tv_rss.html` - 电视剧RSS订阅
- `user_rss.html` - 用户自定义RSS
- `rss_calendar.html` - RSS日历
- `rss_history.html` - RSS历史记录
- `rss_parser.html` - RSS解析器配置

**重命名整理模块** (`rename/`):
- `history.html` - 整理历史记录
- `mediafile.html` - 媒体文件管理
- `tmdbcache.html` - TMDB缓存管理
- `unidentification.html` - 未识别文件处理

**站点管理模块** (`site/`):
- `brushtask.html` - 刷流任务管理
- `resources.html` - 资源管理
- `site.html` - 站点配置
- `sitelist.html` - 站点列表
- `statistics.html` - 统计信息

**设置模块** (`setting/`):
- `basic.html` - 基础设置
- `customwords.html` - 自定义识别词
- `directorysync.html` - 目录同步设置
- `download_setting.html` - 下载设置
- `downloader.html` - 下载器配置
- `filterrule.html` - 过滤规则
- `indexer.html` - 索引器设置
- `library.html` - 媒体库设置
- `mediaserver.html` - 媒体服务器配置
- `notification.html` - 通知设置
- `plugin.html` - 插件管理
- `users.html` - 用户管理

**公共模块** (`macro/`):
- `form.html` - 表单宏
- `head.html` - 页面头部宏
- `oops.html` - 错误页面宏
- `svg.html` - SVG图标宏

#### 导航结构设计
```mermaid
graph TD
    A[侧边栏导航] --> B[首页]
    A --> C[搜索]
    A --> D[发现]
    A --> E[下载]
    A --> F[订阅]
    A --> G[RSS]
    A --> H[整理]
    A --> I[站点]
    A --> J[设置]
    A --> K[服务]
    
    D --> D1[媒体信息]
    D --> D2[人物信息]
    D --> D3[排行榜]
    D --> D4[推荐]
    
    E --> E1[下载中]
    E --> E2[种子删除]
    
    F --> F1[电影订阅]
    F --> F2[电视订阅]
    
    G --> G1[电影RSS]
    G --> G2[电视RSS]
    G --> G3[用户RSS]
    G --> G4[RSS日历]
    G --> G5[RSS历史]
    G --> G6[RSS解析器]
    
    H --> H1[整理历史]
    H --> H2[媒体文件]
    H --> H3[TMDB缓存]
    H --> H4[未识别文件]
    
    I --> I1[刷流任务]
    I --> I2[资源管理]
    I --> I3[站点配置]
    I --> I4[站点列表]
    I --> I5[统计信息]
    
    J --> J1[基础设置]
    J --> J2[自定义词汇]
    J --> J3[目录同步]
    J --> J4[下载设置]
    J --> J5[下载器]
    J --> J6[过滤规则]
    J --> J7[索引器]
    J --> J8[媒体库]
    J --> J9[媒体服务器]
    J --> J10[通知]
    J --> J11[插件]
    J --> J12[用户管理]
```

### 1.2 旧版API架构完整分析 (apiv1.py)

#### API命名空间和接口统计
基于Flask-RESTX的RESTful API，包含16个命名空间，120+个API端点：

| 命名空间 | 描述 | 主要接口 | 接口数量 |
|----------|------|----------|----------|
| `user` | 用户管理 | login, logout, info | 3个 |
| `system` | 系统管理 | version, restart, path, info, status | 5个 |
| `config` | 配置管理 | get_config, save_config, backup, restore | 8个 |
| `site` | 站点管理 | sites, statistics, test, list, indexers, add, delete, update | 12个 |
| `service` | 服务管理 | media_info, network_test, run | 3个 |
| `subscribe` | 订阅管理 | add, delete, list, search, refresh, movie_date, cache_delete | 8个 |
| `rss` | RSS管理 | add, delete, list, parser, preview, test, refresh | 10个 |
| `recommend` | 推荐系统 | get_recommend, trending | 2个 |
| `search` | 搜索功能 | keyword, media_info, torrents | 5个 |
| `download` | 下载管理 | add, delete, list, start, stop, link, batch | 10个 |
| `organization` | 媒体整理 | manual, auto, history, cache, unidentified | 6个 |
| `torrentremover` | 自动删种 | add, delete, list | 3个 |
| `library` | 媒体库 | sync, statistics, space, playhistory, resume | 8个 |
| `brushtask` | 刷流任务 | add, delete, list, start, stop, log | 8个 |
| `media` | 媒体信息 | search, info, cache, person, similar | 12个 |
| `sync` | 目录同步 | add, delete, list, start, stop, log | 6个 |
| `filterrule` | 过滤规则 | add, delete, list, test, import | 5个 |
| `words` | 识别词 | add, delete, list, import, export, test | 6个 |
| `message` | 消息通知 | send, test, config, history | 4个 |
| `plugin` | 插件系统 | list, install, uninstall, config, reload | 8个 |

#### 关键API接口详细分析

**用户认证相关**:
- `POST /api/v1/user/login` - 用户登录，返回Token
- `POST /api/v1/user/logout` - 用户登出
- `POST /api/v1/user/info` - 获取用户信息

**系统管理相关**:
- `POST /api/v1/system/version` - 查询版本信息
- `POST /api/v1/system/restart` - 重启系统
- `POST /api/v1/system/path` - 查询目录路径
- `POST /api/v1/system/info` - 获取系统信息
- `POST /api/v1/system/status` - 获取系统状态

**站点管理相关**:
- `GET /api/v1/site/sites` - 获取所有站点配置
- `GET /api/v1/site/statistics` - 获取站点统计数据
- `POST /api/v1/site/list` - 查询站点列表
- `POST /api/v1/site/test` - 测试站点连接
- `POST /api/v1/site/add` - 添加站点
- `POST /api/v1/site/delete` - 删除站点
- `POST /api/v1/site/update` - 更新站点配置

**搜索下载相关**:
- `POST /api/v1/search/keyword` - 关键字搜索
- `POST /api/v1/search/media` - 媒体信息搜索
- `POST /api/v1/download/add` - 添加下载任务
- `POST /api/v1/download/delete` - 删除下载任务
- `POST /api/v1/download/list` - 获取下载列表
- `POST /api/v1/download/start` - 开始下载任务
- `POST /api/v1/download/stop` - 停止下载任务

**订阅管理相关**:
- `POST /api/v1/subscribe/add` - 新增/修改订阅
- `POST /api/v1/subscribe/delete` - 删除订阅
- `POST /api/v1/subscribe/movie/list` - 查询电影订阅
- `POST /api/v1/subscribe/tv/list` - 查询电视剧订阅
- `POST /api/v1/subscribe/refresh` - 刷新订阅
- `POST /api/v1/subscribe/search` - 订阅搜索

**RSS管理相关**:
- `POST /api/v1/rss/add` - 新增/修改RSS任务
- `POST /api/v1/rss/delete` - 删除RSS任务
- `POST /api/v1/rss/list` - 查询所有RSS任务
- `POST /api/v1/rss/parser/list` - 查询所有解析器
- `POST /api/v1/rss/preview` - RSS预览
- `POST /api/v1/rss/test` - 测试RSS

#### 认证机制
- **认证方式**: Bearer Token + API Key双重认证
- **权限控制**: 基于装饰器的权限验证 (`@require_auth`, `@login_required`)
- **会话管理**: Flask-Login会话管理

#### 数据格式标准
```json
{
  "code": 0,
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

### 1.3 新版系统现状评估

#### 新版前端现状 (frontend/)
**已搭建的基础架构**:
- ✅ Vue 3.4+ 已配置
- ✅ TypeScript 5.0+ 已配置
- ✅ Vite 5.0+ 已配置
- ✅ Element Plus 2.4+ 已配置
- ✅ Pinia 2.1+ 已配置
- ✅ Vue Router 4.2+ 已配置
- ✅ Axios 1.6+ 已配置
- ✅ SCSS 已配置
- ✅ ESLint + Prettier 已配置

**已实现的页面模块**:
- ✅ 登录页面 (`pages/auth/`)
- ✅ 仪表板 (`pages/dashboard/`)
- ✅ 下载管理 (`pages/download/`)
- ✅ 媒体管理 (`pages/media/`)
- ✅ 设置页面 (`pages/settings/`)
- ✅ 站点管理 (`pages/site/`, `pages/sites/`)
- ✅ 订阅管理 (`pages/subscription/`)

**已实现的组件**:
- ✅ 应用加载组件 (`AppLoading.vue`)
- ✅ 语言切换器 (`LanguageSwitcher.vue`)
- ✅ 安全密码输入 (`SecurePasswordInput.vue`)
- ✅ 主题切换器 (`ThemeSwitcher.vue`)
- ✅ 默认布局 (`DefaultLayout.vue`)

#### 新版API现状 (apiv2.py)
**已实现的API命名空间**:
- ✅ `auth` - 认证管理 (login, profile)
- ✅ `system` - 系统管理 (status, info, restart)
- ✅ `sites` - 站点管理 (list, detail, test)
- ✅ `config` - 配置管理 (get, update)
- 🔄 `downloads` - 下载管理 (待实现)
- 🔄 `subscriptions` - 订阅管理 (待实现)
- 🔄 `media` - 媒体管理 (待实现)
- 🔄 `dashboard` - 仪表板数据 (待实现)

**已实现的API接口**:
- ✅ `POST /api/v2/auth/login` - 用户登录 (JWT认证)
- ✅ `GET /api/v2/auth/profile` - 获取用户信息
- ✅ `GET /api/v2/system/status` - 获取系统状态
- ✅ `GET /api/v2/system/info` - 获取系统信息
- ✅ `POST /api/v2/system/restart` - 重启系统
- ✅ `GET /api/v2/sites` - 获取站点列表
- ✅ `GET /api/v2/sites/<id>` - 获取站点详情
- ✅ `POST /api/v2/sites/<id>/test` - 测试站点连接
- ✅ `GET /api/v2/config/system` - 获取系统配置
- ✅ `PUT /api/v2/config/system` - 更新系统配置

**待迁移的API接口**: 约90+个接口需要从v1迁移到v2

### 1.4 参考前端项目分析

#### Geeker-Admin 项目 (可借鉴功能)
**核心组件**:
- 🎯 **ProTable组件** - 高级数据表格，支持搜索、分页、排序、导出
- 🎯 **SearchForm组件** - 动态搜索表单生成
- 🎯 **权限管理系统** - 按钮级权限控制
- 🎯 **主题切换** - 深色/浅色主题
- 🎯 **多布局支持** - 经典、列式、横向、垂直布局
- 🎯 **国际化支持** - 多语言切换
- 🎯 **错误处理** - 统一错误处理机制

#### MoviePilot-Frontend 项目 (可借鉴功能)
**媒体相关组件**:
- 🎯 **媒体卡片组件** - 电影/电视剧展示卡片
- 🎯 **文件浏览器** - 文件目录浏览组件
- 🎯 **下载管理界面** - 下载任务管理
- 🎯 **订阅管理界面** - 媒体订阅管理
- 🎯 **站点管理界面** - PT站点管理
- 🎯 **插件系统界面** - 插件管理
- 🎯 **PWA支持** - 渐进式Web应用
- 🎯 **状态恢复** - 页面状态恢复
- 🎯 **离线检测** - 网络状态检测

---

## 第二阶段：重构设计

### 2.1 技术栈选择

#### 前端技术栈 (已确定)
```json
{
  "核心框架": "Vue 3.4+",
  "开发语言": "TypeScript 5.0+",
  "构建工具": "Vite 5.0+",
  "UI组件库": "Element Plus 2.4+",
  "状态管理": "Pinia 2.1+",
  "路由管理": "Vue Router 4.2+",
  "HTTP客户端": "Axios 1.6+",
  "CSS预处理": "SCSS",
  "图标库": "Element Plus Icons",
  "工具库": "Lodash-es, Day.js"
}
```

#### 开发工具链 (已配置)
```json
{
  "代码规范": "ESLint + Prettier",
  "类型检查": "TypeScript",
  "测试框架": "Vitest + Vue Test Utils",
  "包管理器": "pnpm",
  "Git钩子": "Husky + lint-staged"
}
```

### 2.2 新前端界面设计规划

#### 整体布局设计
```mermaid
graph TD
    A[App.vue] --> B[DefaultLayout.vue]
    B --> C[Header 顶部区域]
    B --> D[Sidebar 侧边栏导航]
    B --> E[Main 主内容区]
    B --> F[Footer 底部区域]
    
    C --> C1[Logo/标题]
    C --> C2[用户信息]
    C --> C3[主题切换]
    C --> C4[语言切换]
    C --> C5[系统状态]
    C --> C6[通知中心]
    
    D --> D1[仪表板]
    D --> D2[搜索]
    D --> D3[发现推荐]
    D --> D4[下载管理]
    D --> D5[订阅管理]
    D --> D6[RSS管理]
    D --> D7[媒体整理]
    D --> D8[站点管理]
    D --> D9[系统设置]
    D --> D10[插件管理]
    
    E --> E1[路由视图]
    E --> E2[面包屑导航]
    E --> E3[页面内容]
    E --> E4[操作按钮]
```

#### 侧边栏导航结构
```
📊 仪表板 (Dashboard)
🔍 搜索 (Search)
🎬 发现推荐 (Discovery)
  ├── 📺 媒体信息 (Media Info)
  ├── 👤 人物信息 (Person Info)
  ├── 📈 排行榜 (Ranking)
  └── ⭐ 推荐内容 (Recommend)
⬇️ 下载管理 (Downloads)
  ├── 📥 下载中 (Downloading)
  └── 🗑️ 种子删除 (Torrent Remove)
📋 订阅管理 (Subscriptions)
  ├── 🎬 电影订阅 (Movie Subscribe)
  └── 📺 电视订阅 (TV Subscribe)
📡 RSS管理 (RSS)
  ├── 🎬 电影RSS (Movie RSS)
  ├── 📺 电视RSS (TV RSS)
  ├── 👤 用户RSS (User RSS)
  ├── 📅 RSS日历 (RSS Calendar)
  ├── 📜 RSS历史 (RSS History)
  └── ⚙️ RSS解析器 (RSS Parser)
📁 媒体整理 (Organization)
  ├── 📜 整理历史 (History)
  ├── 📄 媒体文件 (Media Files)
  ├── 💾 TMDB缓存 (TMDB Cache)
  └── ❓ 未识别文件 (Unidentified)
🌐 站点管理 (Sites)
  ├── 🚀 刷流任务 (Brush Tasks)
  ├── 📦 资源管理 (Resources)
  ├── ⚙️ 站点配置 (Site Config)
  ├── 📋 站点列表 (Site List)
  └── 📊 统计信息 (Statistics)
⚙️ 系统设置 (Settings)
  ├── 🔧 基础设置 (Basic)
  ├── 📝 自定义词汇 (Custom Words)
  ├── 🔄 目录同步 (Directory Sync)
  ├── ⬇️ 下载设置 (Download Settings)
  ├── 📥 下载器 (Downloader)
  ├── 🔍 过滤规则 (Filter Rules)
  ├── 🔗 索引器 (Indexer)
  ├── 📚 媒体库 (Library)
  ├── 🖥️ 媒体服务器 (Media Server)
  ├── 📢 通知设置 (Notification)
  └── 👥 用户管理 (Users)
🔌 插件管理 (Plugins)
🛠️ 服务管理 (Services)
```

#### 顶部区域功能设计
```
[Logo] [系统标题]                    [搜索框] [通知🔔] [用户头像▼] [主题🌙] [语言🌐] [设置⚙️]
```

**顶部功能组件**:
- **Logo和标题**: NAS-Tools品牌标识
- **全局搜索**: 快速搜索功能
- **通知中心**: 系统通知和消息
- **用户菜单**: 用户信息、退出登录
- **主题切换**: 深色/浅色模式
- **语言切换**: 中文/英文
- **系统设置**: 快速设置入口

#### 主内容区设计
**通用页面结构**:
```
[面包屑导航] [页面标题]                                    [操作按钮组]
─────────────────────────────────────────────────────────────────
[搜索/筛选区域]
─────────────────────────────────────────────────────────────────
[数据表格/卡片列表/表单内容]
─────────────────────────────────────────────────────────────────
[分页/状态栏]
```

**响应式设计**:
- **桌面端**: 完整侧边栏 + 主内容区
- **平板端**: 可折叠侧边栏 + 主内容区
- **手机端**: 底部导航 + 全屏主内容区

### 2.3 API迁移映射表

#### 详细API迁移对照表

| 功能模块 | 旧API路径 (v1) | 新API路径 (v2) | 迁移状态 | 数据格式变更 | 备注 |
|----------|----------------|----------------|----------|-------------|------|
| **用户认证** |
| 用户登录 | `POST /api/v1/user/login` | `POST /api/v2/auth/login` | ✅ 已完成 | JWT Token | 改用JWT认证 |
| 用户信息 | `POST /api/v1/user/info` | `GET /api/v2/auth/profile` | ✅ 已完成 | 标准化字段 | 改用GET方法 |
| 用户登出 | `POST /api/v1/user/logout` | `POST /api/v2/auth/logout` | 🔄 待实现 | 无变更 | 需实现 |
| **系统管理** |
| 系统状态 | `POST /api/v1/system/status` | `GET /api/v2/system/status` | ✅ 已完成 | 增加实时数据 | 改用GET方法 |
| 系统信息 | `POST /api/v1/system/info` | `GET /api/v2/system/info` | ✅ 已完成 | 标准化字段 | 改用GET方法 |
| 系统重启 | `POST /api/v1/system/restart` | `POST /api/v2/system/restart` | ✅ 已完成 | 无变更 | 保持兼容 |
| 版本查询 | `POST /api/v1/system/version` | `GET /api/v2/system/version` | 🔄 待实现 | 标准化字段 | 改用GET方法 |
| 路径查询 | `POST /api/v1/system/path` | `GET /api/v2/system/paths` | 🔄 待实现 | 查询参数化 | 改用GET+参数 |
| **站点管理** |
| 站点列表 | `GET /api/v1/site/sites` | `GET /api/v2/sites` | ✅ 已完成 | 简化结构 | 路径简化 |
| 站点详情 | `POST /api/v1/site/info` | `GET /api/v2/sites/{id}` | ✅ 已完成 | RESTful化 | 使用路径参数 |
| 站点测试 | `POST /api/v1/site/test` | `POST /api/v2/sites/{id}/test` | ✅ 已完成 | RESTful化 | 使用路径参数 |
| 站点统计 | `GET /api/v1/site/statistics` | `GET /api/v2/sites/statistics` | 🔄 待实现 | 增加筛选 | 支持时间范围 |
| 站点添加 | `POST /api/v1/site/add` | `POST /api/v2/sites` | 🔄 待实现 | 标准化字段 | RESTful创建 |
| 站点更新 | `POST /api/v1/site/update` | `PUT /api/v2/sites/{id}` | 🔄 待实现 | RESTful化 | 使用PUT方法 |
| 站点删除 | `POST /api/v1/site/delete` | `DELETE /api/v2/sites/{id}` | 🔄 待实现 | RESTful化 | 使用DELETE方法 |
| 索引器列表 | `POST /api/v1/site/indexers` | `GET /api/v2/sites/indexers` | 🔄 待实现 | 改用GET | 改用GET方法 |
| **配置管理** |
| 获取配置 | `POST /api/v1/config/get` | `GET /api/v2/config/system` | ✅ 已完成 | 分类获取 | 按模块分类 |
| 保存配置 | `POST /api/v1/config/save` | `PUT /api/v2/config/system` | ✅ 已完成 | 分类保存 | 按模块保存 |
| 配置备份 | `POST /api/v1/config/backup` | `POST /api/v2/config/backup` | 🔄 待实现 | 增加元数据 | 添加备份信息 |
| 配置恢复 | `POST /api/v1/config/restore` | `POST /api/v2/config/restore` | 🔄 待实现 | 增加验证 | 添加配置验证 |
| **搜索功能** |
| 关键字搜索 | `POST /api/v1/search/keyword` | `POST /api/v2/search/resources` | 🔄 待实现 | 增加过滤器 | 支持高级搜索 |
| 媒体搜索 | `POST /api/v1/search/media` | `POST /api/v2/search/media` | 🔄 待实现 | 标准化结果 | 统一返回格式 |
| **下载管理** |
| 下载列表 | `POST /api/v1/download/list` | `GET /api/v2/downloads` | 🔄 待实现 | 分页支持 | 支持分页筛选 |
| 添加下载 | `POST /api/v1/download/add` | `POST /api/v2/downloads` | 🔄 待实现 | 标准化参数 | RESTful创建 |
| 删除下载 | `POST /api/v1/download/delete` | `DELETE /api/v2/downloads/{id}` | 🔄 待实现 | RESTful化 | 使用DELETE方法 |
| 开始下载 | `POST /api/v1/download/start` | `POST /api/v2/downloads/{id}/start` | 🔄 待实现 | RESTful化 | 使用路径参数 |
| 停止下载 | `POST /api/v1/download/stop` | `POST /api/v2/downloads/{id}/stop` | 🔄 待实现 | RESTful化 | 使用路径参数 |
| 下载链接 | `POST /api/v1/download/link` | `POST /api/v2/downloads/link` | 🔄 待实现 | 增加验证 | 添加链接验证 |
| **订阅管理** |
| 电影订阅列表 | `POST /api/v1/subscribe/movie/list` | `GET /api/v2/subscriptions/movies` | 🔄 待实现 | 分页支持 | 支持分页筛选 |
| 电视订阅列表 | `POST /api/v1/subscribe/tv/list` | `GET /api/v2/subscriptions/tv` | 🔄 待实现 | 分页支持 | 支持分页筛选 |
| 添加订阅 | `POST /api/v1/subscribe/add` | `POST /api/v2/subscriptions` | 🔄 待实现 | 标准化参数 | RESTful创建 |
| 删除订阅 | `POST /api/v1/subscribe/delete` | `DELETE /api/v2/subscriptions/{id}` | 🔄 待实现 | RESTful化 | 使用DELETE方法 |
| 刷新订阅 | `POST /api/v1/subscribe/refresh` | `POST /api/v2/subscriptions/refresh` | 🔄 待实现 | 批量支持 | 支持批量刷新 |
| 订阅搜索 | `POST /api/v1/subscribe/search` | `POST /api/v2/subscriptions/search` | 🔄 待实现 | 增加过滤器 | 支持高级搜索 |
| **RSS管理** |
| RSS任务列表 | `POST /api/v1/rss/list` | `GET /api/v2/rss/tasks` | 🔄 待实现 | 分页支持 | 支持分页筛选 |
| 添加RSS任务 | `POST /api/v1/rss/add` | `POST /api/v2/rss/tasks` | 🔄 待实现 | 标准化参数 | RESTful创建 |
| 删除RSS任务 | `POST /api/v1/rss/delete` | `DELETE /api/v2/rss/tasks/{id}` | 🔄 待实现 | RESTful化 | 使用DELETE方法 |
| RSS解析器列表 | `POST /api/v1/rss/parser/list` | `GET /api/v2/rss/parsers` | 🔄 待实现 | 改用GET | 改用GET方法 |
| RSS预览 | `POST /api/v1/rss/preview` | `POST /api/v2/rss/tasks/{id}/preview` | 🔄 待实现 | RESTful化 | 使用路径参数 |
| **媒体管理** |
| 媒体搜索 | `POST /api/v1/media/search` | `POST /api/v2/media/search` | 🔄 待实现 | 增加过滤器 | 支持高级搜索 |
| 媒体信息 | `POST /api/v1/media/info` | `GET /api/v2/media/{id}` | 🔄 待实现 | RESTful化 | 使用路径参数 |
| 媒体缓存 | `POST /api/v1/media/cache/list` | `GET /api/v2/media/cache` | 🔄 待实现 | 分页支持 | 支持分页筛选 |
| 人物信息 | `POST /api/v1/media/person` | `GET /api/v2/media/persons/{id}` | 🔄 待实现 | RESTful化 | 使用路径参数 |

#### 需要新建的API接口

| 接口名称 | 路径 | 方法 | 功能描述 | 优先级 |
|----------|------|------|----------|--------|
| 批量操作 | `/api/v2/batch` | POST | 支持批量删除、启用、禁用等操作 | P1 |
| 实时状态 | `/api/v2/dashboard/realtime` | GET | 获取仪表板实时状态数据 | P1 |
| 配置验证 | `/api/v2/config/validate` | POST | 配置项验证和测试 | P2 |
| 文件预览 | `/api/v2/files/preview` | GET | 文件内容预览 | P2 |
| 日志查询 | `/api/v2/logs` | GET | 系统日志查询和筛选 | P2 |
| 统计数据 | `/api/v2/statistics` | GET | 综合统计数据 | P2 |
| 健康检查 | `/api/v2/health` | GET | 系统健康状态检查 | P3 |
| 导入导出 | `/api/v2/import`, `/api/v2/export` | POST | 配置和数据导入导出 | P3 |

#### 需要废弃的接口

| 接口路径 | 废弃原因 | 替代方案 | 废弃时间 |
|----------|----------|----------|----------|
| `/api/v1/old_search` | 性能问题，查询逻辑过时 | `/api/v2/search/resources` | v2.1版本 |
| `/api/v1/legacy_config` | 配置格式过时 | `/api/v2/config/*` | v2.2版本 |
| `/api/v1/batch_*` | 接口设计不统一 | `/api/v2/batch` | v2.1版本 |

### 2.4 功能迁移任务清单

#### P0级别任务 (核心基础功能)

**任务1: 用户认证系统完善**
- **参考文件**:
  - 旧版: `web/templates/login.html`, `web/apiv1.py` (user namespace)
  - 新版: `frontend/src/pages/auth/`, `web/apiv2.py` (auth_ns)
- **参考项目**: Geeker-Admin的登录页面和权限管理
- **迁移重点**:
  - JWT Token认证机制
  - 权限控制系统
  - 登录状态持久化
- **预估工时**: 2天

**任务2: 系统配置管理**
- **参考文件**:
  - 旧版: `web/templates/setting/basic.html`, `web/apiv1.py` (config namespace)
  - 新版: `frontend/src/pages/settings/`, `web/apiv2.py` (config_ns)
- **参考项目**: Geeker-Admin的设置页面组件
- **迁移重点**:
  - 配置项分类管理
  - 实时配置验证
  - 配置备份恢复
- **预估工时**: 3天

#### P1级别任务 (核心业务功能)

**任务3: 仪表板数据展示**
- **参考文件**:
  - 旧版: `web/templates/index.html`
  - 新版: `frontend/src/pages/dashboard/`
- **参考项目**:
  - Geeker-Admin的仪表板组件
  - MoviePilot-Frontend的dashboard页面
- **迁移重点**:
  - 系统状态实时监控
  - 数据可视化图表
  - 快速操作入口
- **预估工时**: 4天

**任务4: 站点管理系统**
- **参考文件**:
  - 旧版: `web/templates/site/`, `web/apiv1.py` (site namespace)
  - 新版: `frontend/src/pages/site/`, `web/apiv2.py` (sites_ns)
- **参考项目**: MoviePilot-Frontend的站点管理页面
- **迁移重点**:
  - 站点配置表单
  - 站点状态监控
  - 批量操作功能
- **预估工时**: 5天

**任务5: 搜索功能系统**
- **参考文件**:
  - 旧版: `web/templates/search.html`, `web/apiv1.py` (search namespace)
  - 新版: `frontend/src/pages/search/` (待创建)
- **参考项目**: MoviePilot-Frontend的搜索界面
- **迁移重点**:
  - 高级搜索表单
  - 搜索结果展示
  - 搜索历史记录
- **预估工时**: 6天

#### P2级别任务 (重要业务功能)

**任务6: 下载管理系统**
- **参考文件**:
  - 旧版: `web/templates/download/`, `web/apiv1.py` (download namespace)
  - 新版: `frontend/src/pages/download/`
- **参考项目**: MoviePilot-Frontend的下载管理页面
- **迁移重点**:
  - 下载任务列表
  - 实时进度更新
  - 批量操作功能
- **预估工时**: 7天

**任务7: 订阅管理系统**
- **参考文件**:
  - 旧版: `web/templates/rss/movie_rss.html`, `web/templates/rss/tv_rss.html`, `web/apiv1.py` (subscribe namespace)
  - 新版: `frontend/src/pages/subscription/`
- **参考项目**: MoviePilot-Frontend的订阅管理页面
- **迁移重点**:
  - 电影/电视剧订阅
  - 订阅规则配置
  - 订阅历史记录
- **预估工时**: 6天

**任务8: RSS管理系统**
- **参考文件**:
  - 旧版: `web/templates/rss/`, `web/apiv1.py` (rss namespace)
  - 新版: `frontend/src/pages/rss/` (待创建)
- **参考项目**: MoviePilot-Frontend的RSS相关功能
- **迁移重点**:
  - RSS任务管理
  - RSS解析器配置
  - RSS预览功能
- **预估工时**: 5天

#### P3级别任务 (扩展功能)

**任务9: 媒体库管理**
- **参考文件**:
  - 旧版: `web/templates/rename/`, `web/apiv1.py` (library, media, organization namespace)
  - 新版: `frontend/src/pages/media/`
- **参考项目**: MoviePilot-Frontend的媒体管理功能
- **迁移重点**:
  - 媒体文件整理
  - TMDB缓存管理
  - 未识别文件处理
- **预估工时**: 8天

**任务10: 刷流任务管理**
- **参考文件**:
  - 旧版: `web/templates/site/brushtask.html`, `web/apiv1.py` (brushtask namespace)
  - 新版: `frontend/src/pages/brushtask/` (待创建)
- **参考项目**: 自主设计，参考站点管理界面
- **迁移重点**:
  - 刷流任务配置
  - 刷流进度监控
  - 刷流统计报告
- **预估工时**: 6天

**任务11: 插件系统界面**
- **参考文件**:
  - 旧版: `web/templates/setting/plugin.html`, `web/apiv1.py` (plugin namespace)
  - 新版: `frontend/src/pages/plugins/` (待创建)
- **参考项目**: MoviePilot-Frontend的插件管理页面
- **迁移重点**:
  - 插件列表展示
  - 插件配置界面
  - 插件状态管理
- **预估工时**: 4天

**任务12: 高级设置页面**
- **参考文件**:
  - 旧版: `web/templates/setting/` (除basic.html外的所有文件)
  - 新版: `frontend/src/pages/settings/` (扩展)
- **参考项目**: Geeker-Admin的设置页面结构
- **迁移重点**:
  - 分类设置页面
  - 设置项验证
  - 设置导入导出
- **预估工时**: 8天

---

## 第三阶段：实施计划

### 3.1 开发时间线和里程碑

#### 第一阶段：基础设施完善 (2周)

**Week 1: 项目基础完善** ✅ **已完成**
- [x] 完善Vue 3项目配置和开发环境
- [x] 设计和实现通用组件库
- [x] 完善API接口类型定义
- [x] 完善开发环境和构建配置

**Week 1 完成总结 (2024-08-04)**:
- ✅ **Vue 3项目配置优化**: 完善了Vite配置、TypeScript严格模式、环境变量管理、代码规范配置
- ✅ **通用组件库实现**: 基于Geeker-Admin设计，实现了ProTable、SearchForm、StatusIndicator、ProgressBar、MediaCard等核心组件
- ✅ **完整类型系统**: 建立了API、Store、全局类型的完整TypeScript类型定义体系
- ✅ **开发环境完善**: 优化了构建配置、代码规范、环境变量管理等开发工具链
- ✅ **开发环境验证**: 前端开发服务器正常启动(http://localhost:3001)，构建流程正常，生产代码可用

**Week 2: 核心组件开发** ✅ **已完成**
- [x] 实现ProTable高级表格组件 (参考Geeker-Admin) ✅ 已在Week1完成
- [x] 实现SearchForm动态搜索组件 ✅ 已在Week1完成
- [x] 实现权限控制系统
- [x] 完善布局和主题系统

**Week 2 完成总结 (2024-08-04)**:
- ✅ **权限控制系统**: 实现了完整的权限管理体系，包括权限工具函数、Vue指令、权限组件、路由守卫和Composable
- ✅ **布局和主题系统**: 实现了响应式布局组件、高级主题切换器、主题配置系统和完整的样式变量体系
- ✅ **生产代码验证**: 前端构建成功，开发服务器正常启动，所有新组件可正常使用

**权限控制系统功能**:
- 权限工具函数 (`hasPermission`, `hasRole`, `hasAllPermissions`)
- Vue指令 (`v-permission`, `v-role`, `v-permission-disabled`)
- 权限包装组件 (`PermissionWrapper`)
- 路由权限守卫和动态菜单生成
- 权限管理Composable (`usePermission`)

**布局和主题系统功能**:
- 响应式主布局组件 (`AppLayout`)
- 权限控制的导航菜单 (`AppMenu`)
- 高级主题切换器 (支持明暗主题和多种主题色)
- 完整的SCSS变量系统和响应式断点
- 面包屑导航和页面容器组件

**里程碑1**: 基础开发环境和核心组件完成 ✅

**里程碑1最终验证 (2024-08-04)**:
- ✅ **登录功能验证**: 前端可正常登录，API调用成功，Token获取正常，页面跳转正常
- ✅ **开发环境验证**: 前端服务器和后端API代理工作正常
- ✅ **构建验证**: 生产构建成功，代码质量验证通过
- ✅ **功能完整性**: 所有基础组件、权限系统、布局主题系统功能正常
- ✅ **技术栈就绪**: Vue 3.5 + TypeScript 5.8 + Element Plus + Pinia + Vue Router完整配置

**登录问题修复记录 (2024-08-04)**:
- ✅ **权限检查修复**: 修复了权限工具函数中admin用户判断逻辑
- ✅ **类型定义修复**: 修复了User类型中role字段的可选性
- ✅ **路由守卫优化**: 添加了auth状态初始化逻辑，确保登录状态正确检查
- ✅ **登录流程优化**: 优化了登录成功后的状态更新和页面跳转逻辑
- ✅ **API响应处理修复**: 修复了axios响应拦截器的数据结构处理问题，确保API响应正确解析

**第一阶段总结**: 基础设施完善工作全部完成，前端框架搭建完毕，核心组件库就绪，权限控制系统完善，可以开始里程碑2的核心功能迁移工作。

#### 第二阶段：核心功能迁移 (4周)

**Week 3: 用户认证和系统管理**
- [ ] 完善JWT认证系统 (任务1)
- [ ] 实现系统配置管理 (任务2)
- [ ] 迁移系统管理相关API
- [ ] 实现权限控制中间件

**Week 4: 仪表板和监控**
- [ ] 开发系统仪表板 (任务3)
- [ ] 实现实时状态监控
- [ ] 集成数据可视化图表
- [ ] 实现WebSocket实时通信

**Week 5-6: 站点和搜索功能**
- [ ] 实现站点管理系统 (任务4)
- [ ] 开发搜索功能界面 (任务5)
- [ ] 迁移站点和搜索相关API
- [ ] 实现批量操作功能

**里程碑2**: 核心管理功能完成，系统可基本使用

#### 第三阶段：业务功能迁移 (6周)

**Week 7-8: 下载和订阅管理**
- [ ] 实现下载管理系统 (任务6)
- [ ] 开发订阅管理界面 (任务7)
- [ ] 实现下载进度实时更新
- [ ] 集成订阅自动化功能

**Week 9-10: RSS和媒体管理**
- [ ] 实现RSS管理系统 (任务8)
- [ ] 开发媒体库管理 (任务9)
- [ ] 实现RSS预览和测试功能
- [ ] 集成媒体文件整理功能

**Week 11-12: 高级功能**
- [ ] 实现刷流任务管理 (任务10)
- [ ] 开发插件系统界面 (任务11)
- [ ] 完善高级设置页面 (任务12)
- [ ] 实现数据导入导出功能

**里程碑3**: 所有业务功能迁移完成

#### 第四阶段：优化和测试 (2周)

**Week 13: 性能优化和Playwright MCP测试**
- [ ] 前端性能优化 (代码分割、懒加载)
- [ ] API性能优化和缓存
- [ ] **Playwright MCP自动化测试** (重点)
  - [ ] 配置生产环境测试流程
  - [ ] 实现登录流程自动化验证
  - [ ] 仪表板功能完整性测试
  - [ ] 站点管理操作流程测试
  - [ ] 搜索功能端到端测试
  - [ ] 响应式界面自动化验证
  - [ ] 生成测试报告和截图证据
- [ ] 编写单元测试和集成测试
- [ ] 进行压力测试和性能测试

**Week 14: 用户体验优化和生产验证**
- [ ] 用户界面优化和响应式适配
- [ ] 错误处理和用户反馈优化
- [ ] **生产代码Playwright MCP验证**
  - [ ] 构建生产版本 (`npm run build`)
  - [ ] 启动生产预览服务器 (`npm run preview`)
  - [ ] 执行完整的Playwright MCP测试套件
  - [ ] 验证所有核心功能在生产环境正常工作
  - [ ] 生成最终测试报告
- [ ] 编写用户文档和部署指南
- [ ] 生产环境部署和验证

**里程碑4**: 项目完成，通过Playwright MCP全面验证，可正式发布

### 3.2 技术架构设计

#### 前端架构设计
```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A[Vue 3 组件]
        B[Element Plus UI]
        C[自定义组件库]
    end

    subgraph "业务逻辑层 (Business Logic Layer)"
        D[Pinia Store]
        E[Composables]
        F[业务工具函数]
    end

    subgraph "数据访问层 (Data Access Layer)"
        G[Axios HTTP客户端]
        H[API接口封装]
        I[WebSocket客户端]
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        J[Vue Router]
        K[国际化 i18n]
        L[主题管理]
        M[权限控制]
    end

    A --> D
    A --> E
    B --> A
    C --> A
    D --> G
    E --> H
    F --> H
    G --> I
    J --> A
    K --> A
    L --> A
    M --> A
```

#### 组件设计规范

**通用组件 (Base Components)**:
- `BaseTable` - 基础表格组件
- `BaseForm` - 基础表单组件
- `BaseDialog` - 基础弹窗组件
- `BaseCard` - 基础卡片组件
- `BaseButton` - 基础按钮组件

**业务组件 (Business Components)**:
- `ProTable` - 高级数据表格 (参考Geeker-Admin)
- `SearchForm` - 动态搜索表单
- `MediaCard` - 媒体展示卡片 (参考MoviePilot)
- `StatusIndicator` - 状态指示器
- `ProgressBar` - 进度条组件

**布局组件 (Layout Components)**:
- `DefaultLayout` - 默认布局
- `HeaderBar` - 顶部导航栏
- `SidebarMenu` - 侧边栏菜单
- `BreadcrumbNav` - 面包屑导航
- `FooterBar` - 底部信息栏

#### API设计规范

**统一响应格式**:
```typescript
interface ApiResponse<T = any> {
  code: number;           // 状态码 (0=成功, >0=业务错误, <0=系统错误)
  success: boolean;       // 操作是否成功
  message: string;        // 响应消息
  data: T;               // 响应数据
  timestamp: number;      // 时间戳
}

// 分页响应格式
interface PaginatedResponse<T> {
  items: T[];            // 数据列表
  total: number;         // 总数量
  page: number;          // 当前页码
  pageSize: number;      // 每页大小
  totalPages: number;    // 总页数
}

// 错误响应格式
interface ErrorResponse {
  code: number;
  success: false;
  message: string;
  error?: {
    type: string;        // 错误类型
    details: string;     // 错误详情
    stack?: string;      // 错误堆栈 (开发环境)
  };
  timestamp: number;
}
```

**RESTful API设计原则**:
- 使用HTTP动词表示操作 (GET/POST/PUT/DELETE)
- 使用名词表示资源 (`/api/v2/sites` 而不是 `/api/v2/getSites`)
- 使用路径参数表示资源ID (`/api/v2/sites/{id}`)
- 使用查询参数进行筛选和分页 (`?page=1&size=20&status=active`)
- 使用HTTP状态码表示结果 (200/201/400/401/404/500)

#### 状态管理设计

**Store模块划分**:
```typescript
// stores/modules/auth.ts - 认证状态
interface AuthState {
  user: User | null;
  token: string | null;
  permissions: string[];
  isLoggedIn: boolean;
}

// stores/modules/system.ts - 系统状态
interface SystemState {
  status: SystemStatus;
  config: SystemConfig;
  notifications: Notification[];
  loading: boolean;
}

// stores/modules/sites.ts - 站点状态
interface SitesState {
  sites: Site[];
  statistics: SiteStatistics;
  selectedSite: Site | null;
  loading: boolean;
}

// stores/modules/downloads.ts - 下载状态
interface DownloadsState {
  tasks: DownloadTask[];
  progress: Record<string, number>;
  filters: DownloadFilters;
  loading: boolean;
}
```

### 3.3 风险评估和应对策略

#### 技术风险

| 风险项 | 风险等级 | 影响范围 | 发生概率 | 应对策略 |
|--------|----------|----------|----------|----------|
| API兼容性问题 | 🔴 高 | 全系统 | 中等 | 1. 制定详细的API测试计划<br>2. 保持v1 API向后兼容<br>3. 分阶段迁移，逐步废弃 |
| 前端性能问题 | 🟡 中 | 用户体验 | 低 | 1. 代码分割和懒加载<br>2. 组件虚拟化<br>3. 性能监控和优化 |
| 数据迁移风险 | 🟡 中 | 数据完整性 | 低 | 1. 完整的数据备份机制<br>2. 迁移脚本测试<br>3. 回滚方案准备 |
| 浏览器兼容性 | 🟢 低 | 部分用户 | 低 | 1. 明确支持的浏览器版本<br>2. 渐进式增强设计<br>3. Polyfill支持 |
| WebSocket连接稳定性 | 🟡 中 | 实时功能 | 中等 | 1. 连接重试机制<br>2. 心跳检测<br>3. 降级到轮询 |

#### 项目风险

| 风险项 | 风险等级 | 影响范围 | 发生概率 | 应对策略 |
|--------|----------|----------|----------|----------|
| 开发进度延期 | 🟡 中 | 项目交付 | 中等 | 1. 采用敏捷开发方法<br>2. 分阶段交付MVP<br>3. 定期进度评估 |
| 需求变更频繁 | 🟡 中 | 开发效率 | 中等 | 1. 需求变更控制流程<br>2. 影响评估机制<br>3. 优先级管理 |
| 团队技能差距 | 🟡 中 | 代码质量 | 低 | 1. 技术培训和分享<br>2. 代码审查机制<br>3. 结对编程 |
| 第三方依赖风险 | 🟢 低 | 系统稳定性 | 低 | 1. 依赖版本锁定<br>2. 定期安全更新<br>3. 备选方案准备 |

#### 应对措施详细说明

**1. 技术预研和验证**
- 在正式开发前进行关键技术的可行性验证
- 搭建原型系统验证架构设计
- 性能基准测试和瓶颈分析

**2. 分阶段交付策略**
- 采用MVP (最小可行产品) 方式，优先交付核心功能
- 每个阶段都有可用的系统版本
- 用户可以提前体验和反馈

**3. 自动化测试体系**
- 单元测试覆盖率 > 80%
- 集成测试覆盖主要业务流程
- E2E测试覆盖关键用户场景
- 自动化部署和回滚

**4. 监控和告警机制**
- 前端性能监控 (页面加载时间、错误率)
- 后端API监控 (响应时间、成功率)
- 系统资源监控 (CPU、内存、磁盘)
- 业务指标监控 (用户活跃度、功能使用率)

**5. 回滚和应急预案**
- 数据库备份和恢复方案
- 代码版本回滚机制
- 服务降级策略
- 应急联系人和处理流程

### 3.4 质量保证计划

#### 代码质量标准

**前端代码规范**:
- TypeScript严格模式
- ESLint + Prettier代码格式化
- Vue 3 Composition API最佳实践
- 组件命名和文件组织规范

**后端代码规范**:
- Python PEP 8编码规范
- 类型注解和文档字符串
- API接口文档自动生成
- 错误处理和日志记录规范

#### 测试策略

**前端测试**:
```typescript
// 单元测试示例 (Vitest + Vue Test Utils)
describe('ProTable Component', () => {
  it('should render table with data', () => {
    const wrapper = mount(ProTable, {
      props: { data: mockData, columns: mockColumns }
    });
    expect(wrapper.find('.el-table').exists()).toBe(true);
  });

  it('should handle search functionality', async () => {
    const wrapper = mount(ProTable);
    await wrapper.find('.search-input').setValue('test');
    expect(wrapper.emitted('search')).toBeTruthy();
  });
});

// 集成测试示例
describe('Site Management Integration', () => {
  it('should create and delete site', async () => {
    // 测试站点创建和删除的完整流程
  });
});
```

**后端测试**:
```python
# API测试示例 (pytest)
def test_site_list_api(client, auth_headers):
    """测试站点列表API"""
    response = client.get('/api/v2/sites', headers=auth_headers)
    assert response.status_code == 200
    assert response.json()['success'] is True
    assert 'data' in response.json()

def test_site_create_api(client, auth_headers):
    """测试站点创建API"""
    site_data = {
        'name': 'Test Site',
        'url': 'https://test.com',
        'username': 'test',
        'password': 'test123'
    }
    response = client.post('/api/v2/sites',
                          json=site_data,
                          headers=auth_headers)
    assert response.status_code == 201
    assert response.json()['success'] is True
```

#### 性能测试

**前端性能指标**:
- 首屏加载时间 < 3秒
- 页面切换时间 < 1秒
- 内存使用 < 100MB
- 包大小 < 2MB (gzipped)

**后端性能指标**:
- API平均响应时间 < 500ms
- 并发用户数 > 100
- 数据库查询时间 < 100ms
- 系统可用性 > 99.9%

#### 用户体验测试

**可用性测试**:
- 新用户上手时间 < 10分钟
- 常用功能操作步骤 < 3步
- 错误恢复时间 < 30秒
- 界面响应式适配测试

**兼容性测试**:
- 浏览器兼容性 (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- 设备兼容性 (桌面、平板、手机)
- 分辨率适配 (1920x1080, 1366x768, 移动端)

---

## 第四阶段：Playwright MCP测试验证

### 4.0 生产代码自动化测试策略

#### Playwright MCP测试环境配置

**测试环境要求**:
- ✅ Alpine Docker环境中已配置Playwright MCP
- ✅ 系统Chromium浏览器 (`/usr/bin/chromium-browser`)
- ✅ MCP配置: `npx -y @playwright/mcp@latest --isolated --no-sandbox`
- ✅ 环境变量: `PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/chromium-browser`

#### 核心测试流程

**1. 生产环境启动验证**
```bash
# 构建生产版本
cd /nas-tools/frontend
npm run build

# 启动生产预览服务器
npm run preview  # 运行在 http://localhost:4173

# 启动后端服务
cd /nas-tools
python3 run.py  # 运行在 http://localhost:3000
```

**2. Playwright MCP自动化测试执行**

**登录流程测试**:
- [ ] 导航到登录页面 (`http://localhost:4173`)
- [ ] 验证页面标题和基本结构
- [ ] 填写用户名 (`admin`) 和密码 (`password`)
- [ ] 点击登录按钮
- [ ] 验证登录成功并跳转到仪表板
- [ ] 截图保存登录成功状态

**仪表板功能测试**:
- [ ] 验证仪表板页面完整加载
- [ ] 检查系统状态卡片显示
- [ ] 验证快速操作按钮可点击
- [ ] 测试侧边栏导航菜单
- [ ] 截图保存仪表板状态

**站点管理测试**:
- [ ] 点击站点管理菜单
- [ ] 验证站点列表正确显示
- [ ] 测试站点操作按钮 (测试、编辑、删除)
- [ ] 验证添加站点按钮功能
- [ ] 截图保存站点管理页面

**搜索功能测试**:
- [ ] 导航到搜索页面
- [ ] 测试搜索表单填写
- [ ] 验证搜索结果展示
- [ ] 测试高级搜索选项
- [ ] 截图保存搜索功能

**响应式界面测试**:
- [ ] 调整浏览器窗口大小模拟移动端
- [ ] 验证移动端菜单正常工作
- [ ] 测试触摸友好的交互元素
- [ ] 截图保存不同分辨率下的界面

**3. 测试报告生成**

每次测试执行后自动生成:
- 📸 **页面截图**: 保存在 `/tmp/playwright-mcp-output/`
- 📋 **DOM快照**: 完整的页面结构分析
- 🔍 **控制台日志**: 前端错误和警告信息
- ✅ **测试结果**: 通过/失败状态记录

#### 测试验证标准

**必须通过的测试项**:
- ✅ 所有页面能正常加载 (无404错误)
- ✅ 登录流程完整可用
- ✅ 主要功能页面可正常访问
- ✅ 表单交互正常工作
- ✅ 页面跳转和导航正确
- ✅ 无JavaScript运行时错误
- ✅ 响应式布局在不同分辨率下正常

**测试失败处理**:
- 🔍 分析截图和DOM快照定位问题
- 🐛 修复发现的功能或界面问题
- 🔄 重新执行测试直到全部通过
- 📝 记录问题和解决方案

---

## 第五阶段：验收标准

### 5.1 功能验收标准

#### 核心功能验收

**用户认证系统**:
- [ ] 支持用户名密码登录
- [ ] JWT Token认证机制
- [ ] 权限控制和角色管理
- [ ] 登录状态持久化
- [ ] 安全退出功能

**系统管理功能**:
- [ ] 系统状态实时监控
- [ ] 系统配置管理
- [ ] 系统重启功能
- [ ] 日志查询和下载
- [ ] 系统信息展示

**站点管理功能**:
- [ ] 站点添加、编辑、删除
- [ ] 站点连接测试
- [ ] 站点统计数据
- [ ] 批量操作支持
- [ ] 站点状态监控

**搜索下载功能**:
- [ ] 关键字搜索
- [ ] 高级搜索筛选
- [ ] 搜索结果展示
- [ ] 下载任务管理
- [ ] 下载进度实时更新

**订阅管理功能**:
- [ ] 电影/电视剧订阅
- [ ] 订阅规则配置
- [ ] 自动订阅执行
- [ ] 订阅历史记录
- [ ] 订阅状态管理

#### 界面交互验收

**响应式设计**:
- [ ] 桌面端完整功能展示
- [ ] 平板端适配优化
- [ ] 手机端核心功能可用
- [ ] 不同分辨率适配

**用户体验**:
- [ ] 界面操作直观易懂
- [ ] 加载状态明确提示
- [ ] 错误信息友好展示
- [ ] 操作反馈及时准确

### 4.2 性能验收标准

#### 前端性能标准

| 指标 | 目标值 | 测试方法 |
|------|--------|----------|
| 首屏加载时间 | < 3秒 | Lighthouse测试 |
| 页面切换时间 | < 1秒 | 手动计时测试 |
| 内存使用量 | < 100MB | Chrome DevTools |
| 包大小 | < 2MB (gzipped) | Webpack Bundle Analyzer |
| 交互响应时间 | < 100ms | 用户操作测试 |

#### 后端性能标准

| 指标 | 目标值 | 测试方法 |
|------|--------|----------|
| API平均响应时间 | < 500ms | 压力测试工具 |
| 并发用户支持 | > 100用户 | JMeter压力测试 |
| 数据库查询时间 | < 100ms | 数据库性能监控 |
| 系统可用性 | > 99.9% | 长期监控统计 |
| 内存使用率 | < 80% | 系统监控工具 |

### 4.3 Playwright MCP配置和使用指南

#### MCP环境配置

**Alpine Docker环境要求**:
```bash
# 1. 安装系统依赖
apk add --no-cache chromium nss freetype freetype-dev harfbuzz ca-certificates ttf-freefont glib libstdc++

# 2. 创建Chrome符号链接
mkdir -p /opt/google/chrome
ln -sf /usr/bin/chromium-browser /opt/google/chrome/chrome

# 3. 验证Chromium安装
/usr/bin/chromium-browser --version
```

**MCP服务器配置**:
- **Name**: `Playwright`
- **Command**: `npx -y @playwright/mcp@latest --isolated --no-sandbox`
- **Environment Variables**:
  - `PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH`: `/usr/bin/chromium-browser`
  - `PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD`: `1`
  - `PLAYWRIGHT_BROWSERS_PATH`: `/usr/bin`

#### 标准测试流程

**1. 环境准备**:
```bash
# 构建生产版本
cd /nas-tools/frontend && npm run build

# 启动生产预览服务器
npm run preview  # http://localhost:4173

# 启动后端服务 (新终端)
cd /nas-tools && python3 run.py  # http://localhost:3000
```

**2. 核心功能测试清单**:
- [ ] **登录流程**: 用户名/密码输入 → 登录按钮 → 跳转验证
- [ ] **仪表板**: 页面加载 → 数据显示 → 交互元素测试
- [ ] **站点管理**: 列表显示 → 添加/编辑/删除操作 → 表单验证
- [ ] **搜索功能**: 搜索表单 → 结果展示 → 高级选项
- [ ] **设置页面**: 配置项显示 → 保存功能 → 状态反馈
- [ ] **响应式**: 窗口调整 → 移动端布局 → 触摸交互

**3. 测试验证标准**:
- ✅ 页面无404/500错误
- ✅ JavaScript无运行时错误
- ✅ 表单提交正常响应
- ✅ 页面跳转和导航正确
- ✅ 数据加载和显示正常
- ✅ 移动端布局适配正确

**4. 测试输出**:
- 📸 **截图文件**: 保存在指定目录，记录页面状态
- 📋 **DOM快照**: 完整的页面结构分析
- 🔍 **控制台日志**: 前端错误和警告信息
- ✅ **测试报告**: 通过/失败状态和问题记录

### 4.4 兼容性验收标准

#### 浏览器兼容性

| 浏览器 | 版本要求 | 功能支持度 | 测试状态 |
|--------|----------|------------|----------|
| Chrome | 90+ | 100% | ✅ 完全支持 |
| Firefox | 88+ | 100% | ✅ 完全支持 |
| Safari | 14+ | 95% | ✅ 基本支持 |
| Edge | 90+ | 100% | ✅ 完全支持 |
| 移动浏览器 | 现代版本 | 90% | ✅ 核心功能支持 |

#### 设备兼容性

| 设备类型 | 分辨率范围 | 功能支持度 | 测试状态 |
|----------|------------|------------|----------|
| 桌面端 | 1920x1080+ | 100% | ✅ 完全支持 |
| 笔记本 | 1366x768+ | 100% | ✅ 完全支持 |
| 平板 | 768x1024+ | 90% | ✅ 主要功能支持 |
| 手机 | 375x667+ | 80% | ✅ 核心功能支持 |

### 4.4 安全验收标准

#### 认证安全

- [ ] JWT Token安全实现
- [ ] 密码加密存储
- [ ] 会话超时处理
- [ ] 防止暴力破解
- [ ] API访问权限控制

#### 数据安全

- [ ] 敏感数据加密传输
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF攻击防护
- [ ] 文件上传安全检查

#### 系统安全

- [ ] 错误信息不泄露敏感数据
- [ ] 日志记录安全事件
- [ ] 定期安全漏洞扫描
- [ ] 依赖包安全更新
- [ ] 生产环境安全配置

---

## 总结

### 项目价值和意义

本次NAS-Tools前后端分离重构将带来以下价值：

1. **技术现代化**: 从传统的服务端渲染架构升级到现代化的前后端分离架构
2. **用户体验提升**: 更快的页面响应、更流畅的交互、更好的视觉设计
3. **开发效率提升**: 前后端并行开发、组件化复用、自动化测试
4. **系统可维护性**: 清晰的代码结构、完善的文档、标准化的开发流程
5. **扩展性增强**: 为未来的移动端应用、第三方集成、微服务架构奠定基础

### 关键成功因素

1. **团队技能准备**: 确保团队掌握Vue 3、TypeScript、现代前端开发技能
2. **渐进式迁移**: 采用分阶段迁移策略，降低风险，确保系统稳定
3. **用户参与**: 及时收集用户反馈，持续优化用户体验
4. **质量保证**: 建立完善的测试体系，确保代码质量和系统稳定性
5. **文档完善**: 提供完整的开发文档、用户手册、部署指南

### 风险控制措施

1. **技术风险**: 充分的技术预研、原型验证、性能测试
2. **进度风险**: 敏捷开发方法、分阶段交付、定期评估调整
3. **质量风险**: 自动化测试、代码审查、持续集成
4. **用户风险**: 用户培训、平滑迁移、回滚方案

### 后续发展规划

重构完成后的发展方向：

1. **移动端应用**: 基于统一API开发iOS/Android应用
2. **微服务架构**: 进一步拆分后端服务，实现真正的微服务架构
3. **AI智能化**: 集成机器学习算法，提供智能推荐和自动化功能
4. **生态建设**: 开放API接口，支持第三方插件和集成
5. **云原生部署**: 支持Docker容器化、Kubernetes编排、云平台部署

### 预期成果

通过14周的系统性重构，最终将交付：

- ✅ 现代化的前端应用 (Vue 3 + TypeScript + Element Plus)
- ✅ 标准化的RESTful API (Flask + JWT认证)
- ✅ 完整的功能迁移 (保留所有原有功能)
- ✅ 优秀的用户体验 (响应式设计、实时更新)
- ✅ 完善的开发文档 (技术文档、用户手册)
- ✅ 自动化测试体系 (单元测试、集成测试、E2E测试)
- ✅ 生产就绪的系统 (性能优化、安全加固、监控告警)

这将是一个具有现代化架构、优秀用户体验、高可维护性的NAS媒体管理系统，为用户提供更好的服务，为开发团队提供更高效的开发体验。

---

## 附录：Playwright MCP测试检查清单

### 🔍 每个功能模块完成后必须执行的测试

#### 基础环境检查
- [ ] 前端生产构建成功 (`npm run build`)
- [ ] 生产预览服务器启动 (`npm run preview`)
- [ ] 后端服务正常运行 (`python3 run.py`)
- [ ] Playwright MCP配置正确

#### 登录系统测试
- [ ] 访问登录页面 (http://localhost:4173)
- [ ] 页面标题和基本结构正确
- [ ] 用户名输入框可正常填写
- [ ] 密码输入框可正常填写
- [ ] 登录按钮点击响应正常
- [ ] 登录成功后正确跳转到仪表板
- [ ] 截图保存登录流程

#### 仪表板功能测试
- [ ] 仪表板页面完整加载
- [ ] 系统状态卡片正确显示
- [ ] 快速操作按钮可点击
- [ ] 侧边栏导航菜单正常
- [ ] 数据刷新功能正常
- [ ] 截图保存仪表板状态

#### 站点管理测试
- [ ] 站点管理页面正确加载
- [ ] 站点列表正确显示
- [ ] 添加站点按钮功能正常
- [ ] 编辑站点功能正常
- [ ] 删除站点功能正常
- [ ] 站点测试功能正常
- [ ] 表单验证正确工作
- [ ] 截图保存站点管理页面

#### 搜索功能测试
- [ ] 搜索页面正确加载
- [ ] 搜索表单可正常填写
- [ ] 搜索按钮响应正常
- [ ] 搜索结果正确展示
- [ ] 高级搜索选项正常
- [ ] 搜索历史功能正常
- [ ] 截图保存搜索功能

#### 设置页面测试
- [ ] 设置页面正确加载
- [ ] 各配置项正确显示
- [ ] 配置修改功能正常
- [ ] 保存按钮响应正常
- [ ] 重置功能正常
- [ ] 状态反馈正确
- [ ] 截图保存设置页面

#### 响应式界面测试
- [ ] 桌面端布局正常 (1920x1080)
- [ ] 平板端布局正常 (768x1024)
- [ ] 手机端布局正常 (375x667)
- [ ] 侧边栏在移动端正确折叠
- [ ] 表格在移动端正确适配
- [ ] 按钮在移动端大小合适
- [ ] 截图保存不同分辨率界面

#### 错误处理测试
- [ ] 网络错误时显示正确提示
- [ ] 表单验证错误正确显示
- [ ] 权限不足时正确处理
- [ ] 404页面正确显示
- [ ] 500错误正确处理
- [ ] 截图保存错误状态

### 📋 测试通过标准

**必须满足的条件**:
- ✅ 所有页面能正常加载，无404或500错误
- ✅ 所有表单提交正常，数据正确保存
- ✅ 所有按钮点击有正确响应
- ✅ 页面跳转和导航正确
- ✅ 无JavaScript控制台错误
- ✅ 移动端界面布局正常
- ✅ 所有截图已保存作为测试证据

**测试失败处理**:
1. 🔍 分析截图和DOM快照定位问题
2. 🐛 修复发现的功能或界面问题
3. 🔄 重新执行测试直到全部通过
4. 📝 在任务书中记录问题和解决方案
5. ✅ 更新任务进度状态

### 🎯 最终交付验证

**项目完成前的最终验证**:
- [ ] 执行完整的Playwright MCP测试套件
- [ ] 所有核心功能测试通过
- [ ] 所有页面响应式测试通过
- [ ] 生成完整的测试报告和截图集
- [ ] 确认生产环境部署就绪

**交付物清单**:
- 📁 完整的前端生产构建文件
- 📁 后端API服务代码
- 📸 Playwright MCP测试截图集
- 📋 功能测试通过报告
- 📖 部署和使用文档
