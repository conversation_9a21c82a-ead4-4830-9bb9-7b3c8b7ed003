from flask import Blueprint, request, jsonify
from flask_restx import Api, reqparse, Resource
from datetime import datetime
import traceback
import psutil
import platform

from app.brushtask import BrushTask
from app.rsschecker import RssChecker
from app.sites import Sites
from app.utils import TokenCache, SystemUtils
from config import Config
from web.action import WebAction
from web.backend.pro_user import ProUser
from web.security import require_auth, login_required, generate_access_token, identify

# 创建V2 API蓝图
apiv2_bp = Blueprint("apiv2",
                     __name__,
                     url_prefix='/api/v2')

# 创建API实例
Apiv2 = Api(apiv2_bp,
            version="2.0",
            title="NAS-Tools API v2",
            description="NAS-Tools前后端分离架构API v2版本",
            doc="/docs",
            security='Bearer Auth',
            authorizations={"Bearer Auth": {"type": "apiKey", "name": "Authorization", "in": "header"}})

# API命名空间
auth_ns = Apiv2.namespace('auth', description='认证管理')
system_ns = Apiv2.namespace('system', description='系统管理')
config_ns = Apiv2.namespace('config', description='配置管理')
sites_ns = Apiv2.namespace('sites', description='站点管理')
downloads_ns = Apiv2.namespace('downloads', description='下载管理')
subscriptions_ns = Apiv2.namespace('subscriptions', description='订阅管理')
media_ns = Apiv2.namespace('media', description='媒体管理')
dashboard_ns = Apiv2.namespace('dashboard', description='仪表板数据')


# 先定义认证装饰器，再定义资源类
def v2_auth_required(func):
    """
    V2 API认证装饰器 - 严格的JWT Token验证
    """
    from functools import wraps
    import jwt
    from config import Config

    @wraps(func)
    def wrapper(*args, **kwargs):
        def auth_failed(message="安全认证未通过，请检查Token"):
            return error_response(message, code=403)

        # 获取Authorization头
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return auth_failed("缺少Authorization头")

        # 检查Bearer格式
        if not auth_header.startswith("Bearer "):
            return auth_failed("Authorization头格式错误，应为'Bearer <token>'")

        # 提取token
        token = auth_header.replace("Bearer ", "").strip()
        if not token:
            return auth_failed("Token不能为空")

        # 验证JWT token
        try:
            # 获取密钥
            config = Config()
            key = config.get_config("security").get("api_key")
            if not key:
                return auth_failed("服务器配置错误")

            # 解码并验证token
            payload = jwt.decode(token, key=key, algorithms=['HS256'])

            # 验证必要字段
            username = payload.get("username")
            if not username:
                return auth_failed("Token中缺少用户名信息")

            # 验证用户是否存在（可选，增强安全性）
            from web.backend.pro_user import ProUser
            pro_user = ProUser()
            user = pro_user.get_user(username)
            if not user:
                return auth_failed("用户不存在")

            # 将用户信息添加到请求上下文中，供后续使用
            request.current_user = {
                'username': username,
                'user_id': getattr(user, 'id', '0'),
                'permissions': getattr(user, 'pris', '').split(',') if getattr(user, 'pris', '') else []
            }

        except jwt.ExpiredSignatureError:
            return auth_failed("Token已过期，请重新登录")
        except jwt.DecodeError:
            return auth_failed("Token格式错误")
        except jwt.InvalidTokenError:
            return auth_failed("Token无效")
        except Exception as e:
            return auth_failed(f"认证验证失败: {str(e)}")

        return func(*args, **kwargs)
    return wrapper


class ApiV2Resource(Resource):
    """
    V2 API基础资源类 - 需要JWT Token认证
    """
    method_decorators = [v2_auth_required]


class AuthV2Resource(Resource):
    """
    V2 API认证资源类 - 需要JWT Token认证
    """
    method_decorators = [v2_auth_required]


def success_response(data=None, message="操作成功"):
    """
    成功响应格式
    """
    return {
        "code": 0,
        "success": True,
        "message": message,
        "data": data,
        "timestamp": int(datetime.now().timestamp() * 1000)
    }


def error_response(message="操作失败", code=1, data=None):
    """
    错误响应格式
    """
    return {
        "code": code,
        "success": False,
        "message": message,
        "data": data,
        "timestamp": int(datetime.now().timestamp() * 1000)
    }


def handle_api_error(func):
    """
    API错误处理装饰器
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"API Error: {str(e)}")
            print(traceback.format_exc())
            return error_response(f"服务器内部错误: {str(e)}", code=500)
    return wrapper


# ==================== 认证相关API ====================

@auth_ns.route('/login')
class AuthLogin(Resource):
    """用户登录"""
    
    login_parser = reqparse.RequestParser()
    login_parser.add_argument('username', type=str, required=True, help='用户名')
    login_parser.add_argument('password', type=str, required=True, help='密码')
    
    @auth_ns.expect(login_parser)
    @handle_api_error
    def post(self):
        """
        用户登录
        """
        args = self.login_parser.parse_args()
        username = args.get('username')
        password = args.get('password')
        
        if not username or not password:
            return error_response("用户名或密码不能为空", code=400)
        
        # 获取用户信息
        user_info = ProUser().get_user(username)
        if not user_info:
            return error_response("用户名或密码错误", code=401)
        
        # 验证密码
        if not user_info.verify_password(password):
            return error_response("用户名或密码错误", code=401)
        
        # 生成token
        token = generate_access_token(username)
        # V2 API不需要TokenCache，直接验证JWT
        
        # 返回用户信息
        user_data = {
            "id": str(user_info.id),
            "username": user_info.username,
            "permissions": str(user_info.pris).split(",") if user_info.pris else []
        }
        
        response_data = {
            "token": token,
            "user": user_data,
            "expiresIn": 24 * 60 * 60  # 24小时
        }
        
        return success_response(response_data, "登录成功")


@auth_ns.route('/logout')
class AuthLogout(AuthV2Resource):
    """用户登出"""
    
    @handle_api_error
    def post(self):
        """
        用户登出
        """
        token = request.headers.get("Authorization", "").replace("Bearer ", "")
        if token:
            TokenCache.delete(token)
        
        return success_response(message="登出成功")


@auth_ns.route('/profile')
class AuthProfile(AuthV2Resource):
    """获取用户信息"""
    
    @handle_api_error
    def get(self):
        """
        获取当前用户信息
        """
        # 从认证装饰器中获取用户信息
        current_user = getattr(request, 'current_user', None)
        if not current_user:
            return error_response("无法获取用户信息", code=401)

        username = current_user.get('username')
        if not username:
            return error_response("Token中缺少用户信息", code=401)

        user_info = ProUser().get_user(username)
        if not user_info:
            return error_response("用户不存在", code=404)

        user_data = {
            "id": str(user_info.id),
            "username": user_info.username,
            "permissions": str(user_info.pris).split(",") if user_info.pris else []
        }

        return success_response(user_data)


@auth_ns.route('/check')
class AuthCheck(AuthV2Resource):
    """检查token有效性"""
    
    @handle_api_error
    def get(self):
        """
        检查token有效性
        """
        return success_response({"valid": True}, "Token有效")


# ==================== 系统相关API ====================

@system_ns.route('/status')
class SystemStatus(AuthV2Resource):
    """获取系统状态"""

    @handle_api_error
    def get(self):
        """
        获取系统状态信息
        """
        try:
            # 获取真实的系统状态数据
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # 网络IO统计
            net_io = psutil.net_io_counters()

            status_data = {
                "cpu": round(cpu_percent, 1),
                "memory": round(memory.percent, 1),
                "disk": round((disk.used / disk.total) * 100, 1),
                "network": {
                    "upload": net_io.bytes_sent,
                    "download": net_io.bytes_recv
                },
                "services": [
                    {"name": "NAS-Tools", "status": "running", "uptime": 0},
                    {"name": "下载服务", "status": "running", "uptime": 0},
                    {"name": "RSS服务", "status": "running", "uptime": 0}
                ]
            }

            return success_response(status_data)
        except Exception as e:
            # 如果获取系统状态失败，返回默认值
            return success_response({
                "cpu": 0,
                "memory": 0,
                "disk": 0,
                "network": {"upload": 0, "download": 0},
                "services": []
            })


@system_ns.route('/info')
class SystemInfo(AuthV2Resource):
    """获取系统信息"""
    
    @handle_api_error
    def get(self):
        """
        获取系统基本信息
        """
        try:
            # 获取真实的系统信息
            import sys
            from config import APP_VERSION

            info_data = {
                "version": APP_VERSION,
                "build_time": "2024-01-01 00:00:00",  # 可以从配置或文件中获取
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "platform": platform.system()
            }

            return success_response(info_data)
        except Exception as e:
            # 如果获取失败，返回默认值
            return success_response({
                "version": "3.2.0",
                "build_time": "Unknown",
                "python_version": "Unknown",
                "platform": "Unknown"
            })


@system_ns.route('/restart')
class SystemRestart(AuthV2Resource):
    """重启系统"""
    
    @handle_api_error
    def post(self):
        """
        重启系统
        """
        result = WebAction().api_action(cmd='restart')
        if result.get('code') == 0:
            return success_response(message="系统重启中...")
        else:
            return error_response(result.get('message', '重启失败'))


# ==================== 站点相关API ====================

@sites_ns.route('')
class SitesList(AuthV2Resource):
    """站点列表"""

    @handle_api_error
    def get(self):
        """
        获取站点列表
        """
        # 调用原有的站点列表API
        result = WebAction().api_action(cmd='get_sites', data={})

        if result.get('code') == 0:
            sites_data = result.get('sites', [])
            return success_response(sites_data)
        else:
            return error_response(result.get('message', '获取站点列表失败'))


@sites_ns.route('/<int:site_id>')
class SiteDetail(AuthV2Resource):
    """站点详情"""
    
    @handle_api_error
    def get(self, site_id):
        """
        获取站点详情
        """
        result = WebAction().api_action(cmd='get_site', data={'id': site_id})
        
        if result.get('code') == 0:
            return success_response(result.get('data'))
        else:
            return error_response(result.get('message', '获取站点详情失败'))


@sites_ns.route('/<int:site_id>/test')
class SiteTest(AuthV2Resource):
    """测试站点连接"""
    
    @handle_api_error
    def post(self, site_id):
        """
        测试站点连接
        """
        result = WebAction().api_action(cmd='test_site', data={'id': site_id})
        
        if result.get('code') == 0:
            return success_response(result.get('data'), "站点连接测试完成")
        else:
            return error_response(result.get('message', '站点连接测试失败'))


# ==================== 配置相关API ====================

@config_ns.route('/system')
class SystemConfig(AuthV2Resource):
    """系统配置"""
    
    @handle_api_error
    def get(self):
        """
        获取系统配置
        """
        config_data = Config().get_config()
        return success_response(config_data)
    
    @handle_api_error
    def put(self):
        """
        更新系统配置
        """
        # 这里需要实现配置更新逻辑
        return success_response(message="配置更新成功")


# 注册错误处理器
@apiv2_bp.errorhandler(404)
def not_found(error):
    return jsonify(error_response("接口不存在", code=404)), 404


@apiv2_bp.errorhandler(500)
def internal_error(error):
    return jsonify(error_response("服务器内部错误", code=500)), 500
