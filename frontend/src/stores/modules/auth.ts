import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api'
import type { User, LoginRequest } from '@/types/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const loginLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const permissions = computed(() => user.value?.permissions || [])

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loginLoading.value = true
      const response = await authApi.login(loginData)

      if (response.success) {
        token.value = response.data.token
        user.value = response.data.user

        // 保存token到localStorage
        localStorage.setItem('token', response.data.token)

        return true
      }
      return false
    } catch (error) {
      console.error('Login error:', error)
      return false
    } finally {
      loginLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = null
      user.value = null
      localStorage.removeItem('token')
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await authApi.getUserInfo()
      if (response.success) {
        user.value = response.data
        return true
      }
      return false
    } catch (error) {
      console.error('Get user info error:', error)
      // 如果获取用户信息失败，清除token
      await logout()
      return false
    }
  }

  // 检查权限
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission) || permissions.value.includes('*')
  }

  // 检查多个权限（任一满足）
  const hasAnyPermission = (permissionList: string[]) => {
    return permissionList.some(permission => hasPermission(permission))
  }

  // 检查多个权限（全部满足）
  const hasAllPermissions = (permissionList: string[]) => {
    return permissionList.every(permission => hasPermission(permission))
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await authApi.refreshToken()
      if (response.success) {
        token.value = response.data.token
        localStorage.setItem('token', response.data.token)
        return true
      }
      return false
    } catch (error) {
      console.error('Refresh token error:', error)
      await logout()
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      const success = await getUserInfo()
      if (!success) {
        await logout()
      }
      return success
    }
    return false
  }

  return {
    // 状态
    token,
    user,
    loginLoading,
    
    // 计算属性
    isLoggedIn,
    permissions,
    
    // 方法
    login,
    logout,
    getUserInfo,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshToken,
    initAuth,
  }
})
