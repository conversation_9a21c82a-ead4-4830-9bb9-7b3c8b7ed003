<template>
  <div class="login-container">
    <!-- 语言切换和主题切换 -->
    <div class="top-controls">
      <ThemeSwitcher />
      <LanguageSwitcher />
    </div>

    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <img src="/logo.png" alt="NAS-Tools" class="logo-img" />
          <h1>{{ t('login.title') }}</h1>
        </div>
        <p>{{ t('login.subtitle') }}</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            :placeholder="t('login.username')"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <SecurePasswordInput
            v-model="loginForm.password"
            :placeholder="t('login.password')"
            size="large"
            prefix-icon="Lock"
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">{{ t('login.rememberMe') }}</el-checkbox>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.loginLoading"
            @click="handleLogin"
          >
            <el-icon class="login-icon"><UserFilled /></el-icon>
            {{ t('login.loginButton') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import SecurePasswordInput from '@/components/SecurePasswordInput.vue'
import type { LoginRequest } from '@/types/api'

const router = useRouter()
const authStore = useAuthStore()
const { t } = useI18n()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 状态变量
const rememberMe = ref(true) // 默认勾选保持登录

// 登录表单数据
const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: t('login.usernameRequired'), trigger: 'blur' },
    { min: 3, max: 20, message: t('login.usernameLength'), trigger: 'blur' },
  ],
  password: [
    { required: true, message: t('login.passwordRequired'), trigger: 'blur' },
    { min: 6, max: 50, message: t('login.passwordLength'), trigger: 'blur' },
  ],
}

// 初始化
const initializeForm = () => {
  // 加载保存的记住登录状态
  const savedRememberMe = localStorage.getItem('rememberMe')
  if (savedRememberMe === 'true') {
    rememberMe.value = true
  }
}

// 页面加载时初始化
initializeForm()

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const success = await authStore.login(loginForm)

    if (success) {
      // 如果选择了保持登录，设置更长的过期时间
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true')
      }

      ElMessage.success(t('login.loginSuccess'))

      // 等待一下确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 100))

      await router.push('/')
    } else {
      ElMessage.error(t('login.loginFailed'))
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('登录失败，请重试')
  }
}
</script>

<style scoped lang="scss">
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.top-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  gap: 12px;
  align-items: center;
}

.login-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    .logo-img {
      width: 32px;
      height: 32px;
    }

    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }

  p {
    color: #909399;
    font-size: 14px;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;

  .login-icon {
    margin-right: 6px;
  }
}
</style>
