// 路由权限守卫
import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'
import { hasPermission, hasRole } from '@/utils/permission'
import { ElMessage } from 'element-plus'

/**
 * 权限路由守卫
 */
export function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to: RouteLocationNormalized, _from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const authStore = useAuthStore()
    
    // 白名单路由，不需要权限检查
    const whiteList = ['/login', '/404', '/403', '/500']
    if (whiteList.includes(to.path)) {
      next()
      return
    }
    
    // 检查是否已登录
    if (!authStore.isLoggedIn) {
      // 未登录，跳转到登录页
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查路由权限
    const routePermission = to.meta?.permission
    const routeRole = to.meta?.role
    
    // 如果路由没有设置权限要求，直接通过
    if (!routePermission && !routeRole) {
      next()
      return
    }
    
    // 检查权限
    let hasAccess = true

    if (routePermission) {
      hasAccess = hasAccess && hasPermission(routePermission as any)
    }

    if (routeRole) {
      hasAccess = hasAccess && hasRole(routeRole as any)
    }
    
    if (hasAccess) {
      next()
    } else {
      // 没有权限，跳转到403页面
      ElMessage.error('您没有权限访问此页面')
      next('/403')
    }
  })
}

/**
 * 动态路由权限过滤
 * 根据用户权限过滤路由
 */
export function filterRoutesByPermission(routes: any[], userPermissions: string[], userRole: string): any[] {
  return routes.filter(route => {
    // 检查路由权限
    if (route.meta?.permission) {
      if (!hasPermission(route.meta.permission)) {
        return false
      }
    }
    
    // 检查路由角色
    if (route.meta?.role) {
      if (!hasRole(route.meta.role)) {
        return false
      }
    }
    
    // 递归过滤子路由
    if (route.children && route.children.length > 0) {
      route.children = filterRoutesByPermission(route.children, userPermissions, userRole)
    }
    
    return true
  })
}

/**
 * 生成用户可访问的菜单
 */
export function generateAccessibleMenus(routes: any[]): any[] {
  const authStore = useAuthStore()
  
  if (!authStore.isLoggedIn) {
    return []
  }
  
  return filterRoutesByPermission(routes, authStore.permissions, authStore.user?.role || '')
    .filter(route => {
      // 过滤掉隐藏的菜单项
      return route.meta?.hideInMenu !== true
    })
    .map(route => ({
      path: route.path,
      name: route.name,
      title: route.meta?.title,
      icon: route.meta?.icon,
      children: route.children ? generateAccessibleMenus(route.children) : undefined
    }))
    .filter(menu => {
      // 如果有子菜单但子菜单为空，则隐藏父菜单
      return !menu.children || menu.children.length > 0
    })
}

/**
 * 检查路由是否可访问
 */
export function isRouteAccessible(route: RouteLocationNormalized): boolean {
  const authStore = useAuthStore()
  
  if (!authStore.isLoggedIn) {
    return false
  }
  
  // 检查权限
  if (route.meta?.permission) {
    if (!hasPermission(route.meta.permission as any)) {
      return false
    }
  }

  // 检查角色
  if (route.meta?.role) {
    if (!hasRole(route.meta.role as any)) {
      return false
    }
  }
  
  return true
}
